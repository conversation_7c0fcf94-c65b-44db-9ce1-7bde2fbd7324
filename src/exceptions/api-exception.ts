import axios from 'axios';
import { ZodError } from 'zod';

/**
 * API Exception helper
 */
export class ApiException {
  /**
   * Extract a user-friendly message from any thrown error
   */
  public static getErrorMessage(
    error: unknown,
    defaultMessage: string = 'An unexpected error occurred.'
  ): string {
    // 1. Zod validation errors
    if (error instanceof ZodError) {
      return error.errors.map((e) => e.message).join('; ');
    }

    // 2. Axios errors (network / HTTP)
    if (axios.isAxiosError(error)) {
      // Try to pull a server‐provided message
      const data = error.response?.data;
      if (
        data &&
        typeof data === 'object' &&
        'message' in data &&
        typeof (data as { message: unknown }).message === 'string'
      ) {
        return (data as { message: string }).message;
      }

      // Fallback by status code
      const status = error.response?.status;
      if (typeof status === 'number') {
        if (status >= 500) {
          return 'Server error occurred. Please try again later.';
        }

        const statusMessages: Record<number, string> = {
          400: 'Invalid request. Please check your input and try again.',
          401: 'Authentication failed. Please log in again.',
          403: 'You do not have permission to perform this action.',
          404: 'The requested resource was not found.',
        };
        return statusMessages[status] ?? defaultMessage;
      }

      // No response? Probably a network failure
      return 'Network error. Please check your connection.';
    }

    // 3. Plain JS Error (fallback)
    if (error instanceof Error) {
      return error.message;
    }

    // 4. All other cases
    return defaultMessage;
  }
}
