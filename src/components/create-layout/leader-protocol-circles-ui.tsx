'use client';

import RenderProtocolUi from '@/components/create-layout/render-protocol-ui';

import Grid from '@mui/material/Grid2';
import {
  attachNanoIdToProtocolCircle,
  ProtocolCircleDetails,
  ProtocolCircleDetailsSchema,
} from '@/types/protocol-circle-details';
import { closestCenter, DndContext, DragEndEvent } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import SortableItem from '@/components/create-layout/sortable-item';
import {
  Autocomplete,
  CircularProgress,
  createFilterOptions,
  TextField,
  Typography,
} from '@mui/material';
import { HeaderPhotoType } from '@/enums/header-photo-type';
import { Dispatch, SetStateAction, useCallback, useEffect, useId, useMemo, useRef, useState } from 'react';
import createLayoutService from '@/services/create-layout-service';
import { Photo } from '@/types/photo';
import ProtocolCircleTileUi from '@/components/create-layout/protocol-circle-tile-ui';
import RequestNewCircleDialog from '@/components/create-layout/request-circle-dialog';
import { ProtocolPhotoDetails } from '@/types/protocol-photo-details';
import { CircleDetails } from '@/types/circle-details';

interface LeaderProtocolCirclesUiProps {
  setCircles: Dispatch<SetStateAction<ProtocolCircleDetails[]>>;
  circles: ProtocolCircleDetails[];
  noPositionsFound: boolean;
  setNoPositionsFound: Dispatch<SetStateAction<boolean>>;
  userId: number;
  selectedParty: CircleDetails | null;
  selectedNoAffiliatedParty: boolean;
}

function LeaderProtocolCirclesUi({
  circles,
  setCircles,
  userId,
  noPositionsFound,
  setNoPositionsFound,
  selectedParty,
  selectedNoAffiliatedParty,
}: LeaderProtocolCirclesUiProps) {
  const [options, setOptions] = useState<ProtocolCircleDetails[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>(searchTerm);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedCircle, setSelectedCircle] = useState<ProtocolCircleDetails | null>(null);
  const [isDialogOpen, setDialogOpen] = useState(false);
  const [showProtocolPositionsLoading, setShowProtocolPositionsLoading] = useState<boolean>(false);
  const [fetchingDefaultCircles, setFetchingDefaultCircles] = useState<boolean>(false);

  // Track which party IDs have had their default circles fetched to prevent infinite loops
  const [defaultCirclesFetchedForParties, setDefaultCirclesFetchedForParties] = useState<Set<number>>(new Set());

  // Track whether the initial circles were pre-existing (from layout editing) or empty (new layout)
  const [hasPreExistingCircles] = useState<boolean>(() => circles.length > 0);

  // Track circles that were auto-fetched (so we can replace them when party changes)
  const [autoFetchedCircleIds, setAutoFetchedCircleIds] = useState<Set<string>>(new Set());

  // Requested Circle Actions
  const [requestedCircleName, setRequestedCircleName] = useState<string>('');
  const [requestedCirclePhoto, setRequestedCirclePhoto] = useState<File | null>(null);

  // Protocol UI
  const [protocolPhotoPositions, setProtocolPhotoPositions] = useState<ProtocolPhotoDetails[]>([]);

  const filter = createFilterOptions<ProtocolCircleDetails>();

  // Determine if search circles should be enabled
  const isSearchCirclesEnabled = useMemo(() => {
    // Enable if "no party affiliation" is selected
    if (selectedNoAffiliatedParty) {
      return true;
    }

    // If there were pre-existing circles (layout editing scenario), always enable search
    // This allows manual addition of more circles to existing layouts
    if (hasPreExistingCircles && selectedParty) {
      return true;
    }

    // For new layouts (no pre-existing circles), enable search only after default fetch attempt
    if (selectedParty) {
      const hasDefaultCirclesBeenAttempted = defaultCirclesFetchedForParties.has(selectedParty.id);

      if (circles.length === 0) {
        return hasDefaultCirclesBeenAttempted; // Only enable after attempting default fetch
      }

      // Check if any circle has poster photos
      const hasAnyPosterPhotos = circles.some(circle =>
        circle.poster_photos && circle.poster_photos.length > 0
      );

      // Enable if no poster photos found AND default circles have been attempted
      return !hasAnyPosterPhotos && hasDefaultCirclesBeenAttempted;
    }

    // Disable by default if no party selection is made
    return false;
  }, [selectedParty, selectedNoAffiliatedParty, circles, defaultCirclesFetchedForParties, hasPreExistingCircles]);

  const fetchData = useCallback(async () => {
    if (searchTerm.length <= 2) return setOptions([]);

    setLoading(true);
    try {
      const data = await createLayoutService.fetchCirclesForProtocol({ searchTerm, userId });
      setOptions(data);
    } catch (error) {
      console.error('Error fetching parties:', error);
    } finally {
      setLoading(false);
    }
  }, [searchTerm, userId]);



  // Function to fetch default leader circles for a party
  const fetchDefaultLeaderCircles = useCallback(async (partyId: number, shouldReplaceAutoFetched: boolean = false) => {
    // If we should replace auto-fetched circles, remove them first
    if (shouldReplaceAutoFetched) {
      setCircles(prevCircles =>
        prevCircles.filter(circle => {
          const circleId = circle.nano_id || (circle.id?.toString() ?? circle.name);
          return !autoFetchedCircleIds.has(circleId);
        })
      );
      setAutoFetchedCircleIds(new Set());
    }

    // Mark this party as attempted before making the API call
    setDefaultCirclesFetchedForParties(prev => new Set(prev).add(partyId));

    setFetchingDefaultCircles(true);
    try {
      const defaultCircles = await createLayoutService.fetchDefaultLeaderCirclesOfParty({
        posterAffiliatedPartyId: partyId,
      });

      if (defaultCircles.length > 0) {
        // Add nano_id to each circle and set proper priorities
        const circlesWithNanoId = defaultCircles.map((circle, index) =>
          attachNanoIdToProtocolCircle({
            ...circle,
            type: HeaderPhotoType.header2, // Default leader circles are H2 photos
            priority: index + 1,
          })
        );

        // Track these circles as auto-fetched
        const newAutoFetchedIds = circlesWithNanoId.map(circle =>
          circle.nano_id || (circle.id?.toString() ?? circle.name)
        );
        setAutoFetchedCircleIds(prev => new Set([...prev, ...newAutoFetchedIds]));

        // Add to existing circles
        setCircles(prevCircles => [...prevCircles, ...circlesWithNanoId]);
      }
      // Note: If defaultCircles.length === 0, we still keep the party marked as attempted
      // This prevents infinite loops and enables manual search functionality
    } catch (error) {
      console.error('Error fetching default leader circles:', error);
      // Don't throw error, just log it - this is not a critical failure
      // Keep the party marked as attempted even on error to prevent retries
    } finally {
      setFetchingDefaultCircles(false);
    }
  }, [setCircles, setDefaultCirclesFetchedForParties, setAutoFetchedCircleIds, autoFetchedCircleIds]);

  //Debounce Period
  useEffect(() => {
    const timeoutId = setTimeout(fetchData, 500);
    return () => clearTimeout(timeoutId);
  }, [fetchData]);

  // Debounce the search term so that it only updates after 500ms of inactivity
  // 500ms - match with our debounce period
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);
    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Use ref to track the last processed party to avoid infinite loops
  const lastProcessedPartyRef = useRef<number | null>(null);
  const lastCirclesLengthRef = useRef<number>(circles.length);

  // Effect to handle party changes and fetch default leader circles
  useEffect(() => {
    // Update the circles length ref
    lastCirclesLengthRef.current = circles.length;

    if (!selectedParty || selectedNoAffiliatedParty || fetchingDefaultCircles) {
      return;
    }

    // Case 1: Layout editing scenario (has pre-existing circles)
    // Don't auto-fetch defaults, just enable manual search
    if (hasPreExistingCircles) {
      return;
    }

    // Case 2: New layout creation scenario (no pre-existing circles)
    const hasDefaultCirclesBeenAttempted = defaultCirclesFetchedForParties.has(selectedParty.id);

    // Check if this is a new party selection (different from last processed)
    const isNewPartySelection = lastProcessedPartyRef.current !== selectedParty.id;

    if (isNewPartySelection) {
      // This is a new party selection
      const hasAutoFetchedCircles = autoFetchedCircleIds.size > 0;

      if (hasAutoFetchedCircles) {
        // Party change scenario - replace auto-fetched circles with new party's defaults
        lastProcessedPartyRef.current = selectedParty.id;
        fetchDefaultLeaderCircles(selectedParty.id, true);
      } else if (!hasDefaultCirclesBeenAttempted) {
        // First time selecting this party and no auto-fetched circles exist
        const hasProtocolCircles = circles.length > 0;
        const hasAnyPosterPhotos = circles.some(circle =>
          circle.poster_photos && circle.poster_photos.length > 0
        );

        // Fetch default circles if no protocol circles exist OR no poster photos found
        if (!hasProtocolCircles || !hasAnyPosterPhotos) {
          lastProcessedPartyRef.current = selectedParty.id;
          fetchDefaultLeaderCircles(selectedParty.id);
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedParty, selectedNoAffiliatedParty, fetchingDefaultCircles, hasPreExistingCircles]);

  // Effect to handle "no party affiliation" selection
  // Remove auto-fetched circles and clear tracking state
  useEffect(() => {
    if (selectedNoAffiliatedParty) {
      // Remove auto-fetched circles if any exist
      if (autoFetchedCircleIds.size > 0) {
        setCircles(prevCircles =>
          prevCircles.filter(circle => {
            const circleId = circle.nano_id || (circle.id?.toString() ?? circle.name);
            return !autoFetchedCircleIds.has(circleId);
          })
        );
      }

      // Clear all tracking state
      setDefaultCirclesFetchedForParties(new Set());
      setAutoFetchedCircleIds(new Set());

      // Reset the last processed party ref
      lastProcessedPartyRef.current = null;
    }
  }, [selectedNoAffiliatedParty, autoFetchedCircleIds, setCircles]);

  //useMemo: It will help to memoize the values, so that it will not be recalculated on every render
  // They will recalculate only when the dependencies change
  const h1PhotosCount = useMemo(() => {
    return circles.filter((c) => c.type === HeaderPhotoType.header1).length;
  }, [circles]);

  const h2PhotosCount = useMemo(() => {
    return circles.filter((c) => c.type === HeaderPhotoType.header2).length;
  }, [circles]);

  const fetchProtocolXYPositions = useCallback(
    async (aborted?: { current: boolean }) => {
      try {
        if (aborted?.current) return;
        setShowProtocolPositionsLoading(true);
        const data = await createLayoutService.fetchProtocolXYPositions({
          h1Count: h1PhotosCount,
          h2Count: h2PhotosCount,
        });
        if (aborted?.current) return;
        setProtocolPhotoPositions(data);
        setNoPositionsFound(false);
      } catch (error) {
        if (aborted?.current) return;
        setNoPositionsFound(true);
        console.debug('Error fetching protocol xy positions:', error);
      } finally {
        if (aborted?.current) return;
        setShowProtocolPositionsLoading(false);
      }
    },
    [h1PhotosCount, h2PhotosCount, setNoPositionsFound]
  );

  useEffect(() => {
    const aborted = { current: false };
    void fetchProtocolXYPositions(aborted);
    return () => {
      aborted.current = true;
    };
  }, [h1PhotosCount, h2PhotosCount, fetchProtocolXYPositions]);

  const getUniqueKey = (circle: ProtocolCircleDetails) => {
    return `${circle.nano_id ?? ''}-${circle.id ?? 'requested'}-${circle.name}`;
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    const updatedCircles = [...circles];

    const oldIndex = updatedCircles.findIndex((circle) => getUniqueKey(circle) === active.id);
    const newIndex = updatedCircles.findIndex((circle) => getUniqueKey(circle) === over.id);

    if (oldIndex === -1 || newIndex === -1) return;

    // Move the dragged circle within the array
    const [movedCircle] = updatedCircles.splice(oldIndex, 1);
    updatedCircles.splice(newIndex, 0, movedCircle);

    // Separate H1 and H2 items
    const header1Circles = updatedCircles.filter((c) => c.type === HeaderPhotoType.header1);
    const header2Circles = updatedCircles.filter((c) => c.type === HeaderPhotoType.header2);

    // We will be adding priority individually based on their type
    // As H1 as priority - 1 and similarly H2 as priority - 1
    const updatedHeader1 = header1Circles.map((circle, index) => ({
      ...circle,
      priority: index + 1,
    }));
    const updatedHeader2 = header2Circles.map((circle, index) => ({
      ...circle,
      priority: index + 1,
    }));

    // Adding all the circles by updating priority individually based on the type
    setCircles([...updatedHeader1, ...updatedHeader2]);
  };

  const onNewProtocolCircleSelected = (selectedCircle: ProtocolCircleDetails | null) => {
    if (!selectedCircle) return;

    const header2Circles = circles.filter((c) => c.type === HeaderPhotoType.header2);

    const newCircle: ProtocolCircleDetails = attachNanoIdToProtocolCircle({
      id: selectedCircle.id,
      name: selectedCircle.name,
      name_en: selectedCircle.name_en,
      type: HeaderPhotoType.header2,
      sub_text: selectedCircle.sub_text,
      priority: header2Circles.length + 1,
      poster_photos: selectedCircle.poster_photos,
    });

    setCircles([...circles, newCircle]);
    setSearchTerm('');
    setSelectedCircle(null);
  };

  // id: number or string
  // why string? for requested circles we will be doing name comparison
  const onPosterPhotoSelected = (id: number | string, selectedPhoto: Photo | null) => {
    setCircles((prevCircles) =>
      prevCircles.map((circle) => {
        if ((circle.nano_id || (circle.id ?? circle.name)) === id) {
          return {
            ...circle,
            photo: selectedPhoto,
            poster_photos: circle.poster_photos.map((photo) => ({
              ...photo,
              selected: photo.url === selectedPhoto?.url, // Mark the selected photo as true, others as false
            })),
          };
        }
        return circle;
      })
    );
  };

  const onHeaderTypeSelect = (id: number | string, type: HeaderPhotoType) => {
    setCircles((prevCircles) => {
      // update the type of the selected circle
      const updatedCircles = prevCircles.map((circle) =>
        (circle.nano_id || (circle.id ?? circle.name)) === id ? { ...circle, type } : circle
      );

      // Now, recalculate priority for each type
      // Here we add all the circles of a type and assign priority based on their order
      // { header1: 1, header2: 1, header1: 2, header2: 2, ... }
      const typePriorityMap: Record<HeaderPhotoType, number> = {} as Record<
        HeaderPhotoType,
        number
      >;

      const finalCircles = updatedCircles.map((circle) => {
        const newPriority = (typePriorityMap[circle.type] || 0) + 1;
        typePriorityMap[circle.type] = newPriority;

        return { ...circle, priority: newPriority };
      });

      return [...finalCircles];
    });
  };

  // Requested Circle Logic
  const requestedCircleMockData: ProtocolCircleDetails = ProtocolCircleDetailsSchema.parse({
    id: -1,
    name: '➕ Request New Circle',
    name_en: '',
  });

  const onCircleRequested = () => {
    const header2CirclesCount = circles.filter((c) => c.type === HeaderPhotoType.header2).length;
    const newCircle: ProtocolCircleDetails = ProtocolCircleDetailsSchema.parse({
      name: requestedCircleName,
      type: HeaderPhotoType.header2,
      priority: header2CirclesCount + 1,
      photo_file: requestedCirclePhoto,
    });

    setCircles([...circles, newCircle]);
    setDialogOpen(false);
    setRequestedCircleName('');
    setRequestedCirclePhoto(null);
    setSearchTerm('');
    setSelectedCircle(null);
  };

  const onCircleDelete = (circleToDelete: ProtocolCircleDetails) => {
    setCircles((prevCircles) => {
      // Remove the circle using a stable unique key
      const filteredCircles = prevCircles.filter(
        (c) => getUniqueKey(c) !== getUniqueKey(circleToDelete)
      );

      // Separate H1 and H2 items
      const header1Circles = filteredCircles.filter((c) => c.type === HeaderPhotoType.header1);
      const header2Circles = filteredCircles.filter((c) => c.type === HeaderPhotoType.header2);

      // Recalculate priorities for each type
      const updatedHeader1 = header1Circles.map((circle, index) => ({
        ...circle,
        priority: index + 1,
      }));
      const updatedHeader2 = header2Circles.map((circle, index) => ({
        ...circle,
        priority: index + 1,
      }));

      // Return the updated circles with recalculated priorities
      return [...updatedHeader1, ...updatedHeader2];
    });
  };

  const id = useId();

  return (
    <Grid
      container
      spacing={2}
      direction="column"
      size="grow"
      marginTop={2}
    >
      <Grid>
        <Typography variant="h4">Protocol</Typography>
      </Grid>
      <Grid size={{ xs: 12, sm: 10, md: 8 }}>
        <Autocomplete
          options={options}
          disabled={!isSearchCirclesEnabled}
          getOptionLabel={(option) => {
            if (typeof option === 'object' && option.id && option.name) {
              // If it's the request circle option, return just the name
              if (option.id === -1) {
                return option.name;
              }
              return `${option.id && option.id !== -1 ? option.id + ' - ' : ''}${option.short_name ? option.short_name + ' - ' : ''}${option.name}${option.name_en.length > 0 ? ' - ' + option.name_en : ''}`;
            }
            return String(option);
          }}
          renderOption={(props, option) => (
            <li
              {...props}
              key={option.id}
              style={{
                color: option.id === -1 ? '#1976d2' : 'inherit',
                fontWeight: option.id === -1 ? 500 : 400,
              }}
            >
              {option.id === -1
                ? option.name
                : `${option.id && option.id !== -1 ? option.id + ' - ' : ''}${option.short_name ? option.short_name + ' - ' : ''}${option.name}${option.name_en.length > 0 ? ' - ' + option.name_en : ''}`}
            </li>
          )}
          filterOptions={(options, params) => {
            const filtered = filter(options, params);
            if (!loading && filtered.length === 0 && debouncedSearchTerm.length > 2) {
              return [requestedCircleMockData];
            }
            return filtered;
          }}
          freeSolo
          value={selectedCircle}
          inputValue={searchTerm}
          onInputChange={(_, newInputValue) => setSearchTerm(newInputValue)}
          onChange={(_, newValue) => {
            if (typeof newValue === 'object' && newValue?.id && newValue?.name) {
              if (newValue.id === -1) {
                setDialogOpen(true);
                if (searchTerm.length > 0) {
                  setRequestedCircleName(searchTerm);
                }
                return;
              }

              setSelectedCircle(newValue);
              onNewProtocolCircleSelected(newValue);
            }
          }}
          loading={loading || fetchingDefaultCircles}
          renderInput={(params) => (
            <TextField
              {...params}
              label={!isSearchCirclesEnabled ? "Select a party affiliation to enable search circles" : "Search Circles"}
              variant="outlined"
              fullWidth
              slotProps={{
                input: {
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {(loading || fetchingDefaultCircles) ? (
                        <CircularProgress
                          color="inherit"
                          size={20}
                        />
                      ) : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                },
              }}
            />
          )}
        />
      </Grid>
      <Grid
        container
        direction="row"
        spacing={2}
        size={12}
        sx={{ display: circles.length === 0 ? 'none' : 'flex' }}
      >
        <Grid
          width={{ xs: '100%', sm: '100%', md: 'calc(100% - 460px)' }}
          height="fit-content"
          container
          direction="column"
          spacing={2}
          sx={{
            // Add minimal bottom padding on mobile for sticky protocol preview
            paddingBottom: { xs: '16px', sm: '16px', md: 0 },
          }}
        >
          <DndContext
            id={id}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            {/* Section for Header 1 */}
            <Grid
              container
              direction="column"
              spacing={2}
            >
              <Typography variant="h6">H1 Photos</Typography>
              <SortableContext
                items={circles
                  .filter((c) => c.type === HeaderPhotoType.header1)
                  .map((c) => getUniqueKey(c))}
                strategy={verticalListSortingStrategy}
              >
                {circles
                  .filter((c) => c.type === HeaderPhotoType.header1)
                  .map((circle) => {
                    const id = getUniqueKey(circle);
                    return (
                      <SortableItem
                        key={id}
                        id={id}
                      >
                        <ProtocolCircleTileUi
                          circle={circle}
                          onHeaderTypeSelect={onHeaderTypeSelect}
                          onPosterPhotoSelected={onPosterPhotoSelected}
                          onCircleDelete={onCircleDelete}
                        />
                      </SortableItem>
                    );
                  })}
              </SortableContext>
            </Grid>

            {/* Section for Header 2 */}
            <Grid
              container
              direction="column"
              spacing={2}
            >
              <Typography variant="h6">H2 Photos</Typography>
              <SortableContext
                items={circles
                  .filter((c) => c.type === HeaderPhotoType.header2)
                  .map((c) => getUniqueKey(c))}
                strategy={verticalListSortingStrategy}
              >
                {circles
                  .filter((c) => c.type === HeaderPhotoType.header2)
                  .map((circle) => {
                    const id = getUniqueKey(circle);
                    return (
                      <SortableItem
                        key={id}
                        id={id}
                      >
                        <ProtocolCircleTileUi
                          circle={circle}
                          onHeaderTypeSelect={onHeaderTypeSelect}
                          onPosterPhotoSelected={onPosterPhotoSelected}
                          onCircleDelete={onCircleDelete}
                        />
                      </SortableItem>
                    );
                  })}
              </SortableContext>
            </Grid>
          </DndContext>
        </Grid>
        <Grid
          sx={{
            // Mobile: Sticky at bottom, Desktop: Sticky at top
            position: 'sticky',
            bottom: { xs: 0, sm: 0, md: 'auto' },
            top: { xs: 'auto', sm: 'auto', md: '16px' },
            width: { xs: '100%', sm: '100%', md: '440px' },
            zIndex: { xs: 1000, sm: 1000, md: 'auto' },
            alignSelf: { xs: 'auto', sm: 'auto', md: 'flex-start' },
            height: 'fit-content',
            // Add some styling for mobile
            backgroundColor: { xs: 'background.paper', sm: 'background.paper', md: 'transparent' },
            paddingBottom: { xs: 1, sm: 1, md: 0 },
          }}
        >
          <RenderProtocolUi
            circles={circles}
            noPositionsFound={noPositionsFound}
            protocolPhotoPositions={protocolPhotoPositions}
            showLoading={showProtocolPositionsLoading}
          />
        </Grid>
      </Grid>
      <Grid>
        <RequestNewCircleDialog
          open={isDialogOpen}
          onClose={() => {
            setDialogOpen(false);
            setRequestedCircleName('');
            setRequestedCirclePhoto(null);
          }}
          onSubmit={onCircleRequested}
          requestedCircleName={requestedCircleName}
          setRequestedCircleName={setRequestedCircleName}
          requestedCirclePhoto={requestedCirclePhoto}
          setRequestedCirclePhoto={setRequestedCirclePhoto}
        />
      </Grid>
    </Grid>
  );
}

export default LeaderProtocolCirclesUi;
