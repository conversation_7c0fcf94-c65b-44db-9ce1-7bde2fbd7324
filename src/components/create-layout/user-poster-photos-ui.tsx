'use client';

import { <PERSON>, <PERSON><PERSON>, <PERSON>lapse, Typo<PERSON>, TextField } from '@mui/material';
import { UserPosterPhoto, UserPosterPhotoType } from '@/types/user-poster-photo';
import { ChangeEvent, Dispatch, SetStateAction, useMemo, useRef, useCallback, useState, useEffect } from 'react';
import Grid from '@mui/material/Grid2';
import {
  DeleteRounded,
  Diversity1Outlined,
  GroupOutlined,
  PersonOutline,
  PersonOutlined,
  PhotoCamera,
} from '@mui/icons-material';
import Image from 'next/image';
import { Photo } from '@/types/photo';

import useMobileDetection from '@/hooks/use-mobile-detection';
import useBlobUrlManager from '@/hooks/use-blob-url-manager';
import useImageCropping from '@/hooks/use-image-cropping';
import MobilePhotoSelectionModal from './mobile-photo-selection-modal';
import ImageCroppingModal from './image-cropping-modal';



const userPosterImageHeight: number = 233.34;
const userPosterImageWidth: number = 208;

// Accepted file types for user poster photos
const acceptedFileTypes: string = '.jpg,.jpeg,.png,.webp';

// These are the Supported photo types
const photoTypes = [
  UserPosterPhotoType.posterPhotoWithoutBackgroundOriginal,
  UserPosterPhotoType.heroFramePhotoOriginal,
  UserPosterPhotoType.familyFramePhotoOriginal,
];

interface UserPosterPhotosUiProps {
  photos: UserPosterPhoto[];
  setPhotos: Dispatch<SetStateAction<UserPosterPhoto[]>>;
  userProfilePhoto: Photo | null | undefined;
  usedUserProfileAsCutoutPhoto: boolean;
  setUsedUserProfileAsCutoutPhoto: Dispatch<SetStateAction<boolean>>;
  familyFrameName: string;
  setFamilyFrameName: Dispatch<SetStateAction<string>>;
}

const getCustomizedStringForPhotoTypes = (type: UserPosterPhotoType): string => {
  switch (type) {
    case UserPosterPhotoType.posterPhotoWithoutBackgroundOriginal:
      return 'Self Photo';
    case UserPosterPhotoType.heroFramePhotoOriginal:
      return 'Hero Frame Photo';
    case UserPosterPhotoType.familyFramePhotoOriginal:
      return 'Family Frame Photo';
    default:
      return 'Upload Photo';
  }
};

const getIconForPhotoTypes = (type: UserPosterPhotoType) => {
  switch (type) {
    case UserPosterPhotoType.posterPhotoWithoutBackgroundOriginal:
      return <PersonOutlined />;
    case UserPosterPhotoType.heroFramePhotoOriginal:
      return <GroupOutlined />;
    case UserPosterPhotoType.familyFramePhotoOriginal:
      return <Diversity1Outlined />;
    default:
      return <PersonOutline />;
  }
};

function UserPosterPhotosUi({
  photos,
  setPhotos,
  userProfilePhoto,
  usedUserProfileAsCutoutPhoto,
  setUsedUserProfileAsCutoutPhoto,
  familyFrameName,
  setFamilyFrameName,
}: UserPosterPhotosUiProps) {
  // Custom hooks
  const isMobile = useMobileDetection();
  const {
    createTrackedBlobUrl,
    revokeBlobUrl,
    revokeBlobUrlForFile,
    revokeAllBlobUrls,
    getBlobUrlForPhotoFile,
  } = useBlobUrlManager();
  const {
    crop,
    setCrop,
    zoom,
    setZoom,
    croppedAreaPixels,
    handleCropComplete,
    getCroppedImg,
    resetCropping,
  } = useImageCropping();

  // Check if family photo exists
  const familyPhotoExists = useMemo(() => {
    return photos.some(
      (photo) =>
        photo.type === UserPosterPhotoType.familyFramePhotoOriginal &&
        (photo.photo || photo.photo_file)
    );
  }, [photos]);

  // State
  const [selectModalOpen, setSelectModalOpen] = useState(false);
  const [cropModalOpen, setCropModalOpen] = useState(false);
  const [cropImage, setCropImage] = useState<string | null>(null);
  const [cropFileType, setCropFileType] = useState<string>('image/jpeg');
  const [currentPhotoType, setCurrentPhotoType] = useState<UserPosterPhotoType | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const cameraInputRef = useRef<HTMLInputElement | null>(null);

  // Local state for family frame name to prevent infinite loops
  const [localFamilyFrameName, setLocalFamilyFrameName] = useState(familyFrameName);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup all blob URLs on unmount
  useEffect(() => {
    return () => {
      revokeAllBlobUrls();
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [revokeAllBlobUrls]);

  // Cleanup crop image when modal closes
  useEffect(() => {
    if (!cropModalOpen && cropImage) {
      if (cropImage.startsWith('blob:')) {
        revokeBlobUrl(cropImage);
      }
      setCropImage(null);
      resetCropping();
    }
  }, [cropModalOpen, cropImage, revokeBlobUrl, resetCropping]);

  // Update local state when prop changes
  useEffect(() => {
    setLocalFamilyFrameName(familyFrameName);
  }, [familyFrameName]);

  // Debounced update to parent
  const handleFamilyFrameNameChange = (value: string) => {
    setLocalFamilyFrameName(value);

    // Clear existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set new timeout
    debounceTimeoutRef.current = setTimeout(() => {
      setFamilyFrameName(value);
    }, 300);
  };

  // Helper function to fetch remote image as blob to avoid CORS issues
  const fetchImageAsBlob = useCallback(async (url: string): Promise<string> => {
    try {
      const response = await fetch(url, { mode: 'cors' });
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status}`);
      }
      const blob = await response.blob();
      return createTrackedBlobUrl(new File([blob], 'remote-image', { type: blob.type }));
    } catch (error) {
      console.warn('Failed to fetch remote image, using original URL:', error);
      return url; // Fallback to original URL
    }
  }, [createTrackedBlobUrl]);

  // Handle after cropping
  const handleCropSave = useCallback(async () => {
    if (!cropImage || !croppedAreaPixels || !currentPhotoType) return;
    try {
      const blob = await getCroppedImg(cropImage, croppedAreaPixels, cropFileType);
      const extension = cropFileType.split('/')[1] ?? 'jpeg'; // e.g. image/png -> png
      const file = new File([blob], `cropped.${extension}`, { type: cropFileType });

      const newPhotos = [...photos];
      const index = newPhotos.findIndex((photo) => photo.type === currentPhotoType);
      if (index >= 0 && newPhotos[index].photo_file) {
        revokeBlobUrlForFile(newPhotos[index].photo_file);
      }
      if (index >= 0) {
        newPhotos[index] = {
          ...newPhotos[index],
          photo: null,
          photo_file: file,
        };
      } else {
        newPhotos.push({
          type: currentPhotoType,
          photo: null,
          photo_file: file,
        });
      }
      setPhotos(newPhotos);
    } catch (err) {
      console.error('Cropping failed', err);
      // TODO: surface an error snackbar/toast to the user
      return;
    }
    setCropModalOpen(false);
    setCropImage(null);
    setCurrentPhotoType(null);
    if (currentPhotoType === UserPosterPhotoType.posterPhotoWithoutBackgroundOriginal) {
      setUsedUserProfileAsCutoutPhoto(false);
    }
    resetCropping();
  }, [cropImage, croppedAreaPixels, cropFileType, getCroppedImg, photos, setPhotos, currentPhotoType, setUsedUserProfileAsCutoutPhoto, revokeBlobUrlForFile, resetCropping]);

  // Handle file/camera selection
  const handleMobileFile = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setCropFileType(file.type);
    setCropImage(createTrackedBlobUrl(file));
    setCropModalOpen(true);
    setSelectModalOpen(false);
  };

  const handleFileChange = (type: UserPosterPhotoType, event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const newPhotos = [...photos];
    const index = newPhotos.findIndex((photo) => photo.type === type);

    // Clean up old blob URL if replacing an existing photo_file
    if (index >= 0 && newPhotos[index].photo_file) {
      revokeBlobUrlForFile(newPhotos[index].photo_file);
    }

    if (type === UserPosterPhotoType.posterPhotoWithoutBackgroundOriginal) {
      setUsedUserProfileAsCutoutPhoto(false);
    }

    if (index >= 0) {
      newPhotos[index] = {
        ...newPhotos[index],
        photo: null,
        photo_file: file,
      };
    } else {
      newPhotos.push({
        type: type,
        photo: null,
        photo_file: file,
      });
    }
    setPhotos(newPhotos);
  };

  const handleRemove = (type: UserPosterPhotoType) => {
    // Find the photo being removed to clean up its blob URL
    const photoToRemove = photos.find((photo) => photo.type === type);
    if (photoToRemove?.photo_file) {
      revokeBlobUrlForFile(photoToRemove.photo_file);
    }

    // Remove the photo from state
    const newPhotos = photos.filter((photo) => photo.type !== type);
    setPhotos(newPhotos);

    // Reset the file‐input(s) for this type so you can re-select the same file
    const clearInput = (id: string) => {
      const inp = document.getElementById(id) as HTMLInputElement | null;
      if (inp) inp.value = '';
    };
    clearInput(`file-input-${type}`);
    clearInput(`file-input-change-${type}`);

    // Reset any other related flags
    if (type === UserPosterPhotoType.posterPhotoWithoutBackgroundOriginal) {
      setUsedUserProfileAsCutoutPhoto(false);
    }
    if (type === UserPosterPhotoType.familyFramePhotoOriginal) {
      setFamilyFrameName('');
    }
  };

  const isUserProfileAlreadySet =
    userProfilePhoto &&
    photos.length > 0 &&
    photos.find((photo) => photo.type === UserPosterPhotoType.posterPhotoWithoutBackgroundOriginal)
      ?.photo?.url === userProfilePhoto.url;

  return (
    <Grid
      container
      direction="column"
      spacing={2}
      marginTop={2}
    >
      {/* Camera/Gallery select modal (mobile only) */}
      <MobilePhotoSelectionModal
        open={selectModalOpen}
        onClose={() => setSelectModalOpen(false)}
        onCamera={() => cameraInputRef.current?.click()}
        onGallery={() => fileInputRef.current?.click()}
      />
      {/* Hidden file inputs for camera/gallery */}
      <input
        ref={cameraInputRef}
        accept={acceptedFileTypes}
        style={{ display: 'none' }}
        type="file"
        capture="environment"
        onChange={handleMobileFile}
      />
      <input
        ref={fileInputRef}
        accept={acceptedFileTypes}
        style={{ display: 'none' }}
        type="file"
        onChange={handleMobileFile}
      />
      {/* Cropping modal */}
      <ImageCroppingModal
        open={cropModalOpen}
        image={cropImage}
        crop={crop}
        setCrop={setCrop}
        zoom={zoom}
        setZoom={setZoom}
        aspect={250 / 280}
        onCropComplete={handleCropComplete}
        onClose={() => setCropModalOpen(false)}
        onSave={handleCropSave}
      />
      <Grid>
        <Typography variant="h4">User Poster Photos</Typography>
      </Grid>
      <Collapse
        in={!!userProfilePhoto && !isUserProfileAlreadySet}
        timeout={500}
      >
        <Grid
          container
          direction="row"
          alignItems="center"
          spacing={2}
        >
          <Typography variant="body1">To Use Profile Photo as Self Photo</Typography>
          <Button
            variant="outlined"
            disabled={usedUserProfileAsCutoutPhoto}
            onClick={async () => {
              if (isMobile && userProfilePhoto?.url) {
                // On mobile, open cropping modal with profile photo
                setCurrentPhotoType(UserPosterPhotoType.posterPhotoWithoutBackgroundOriginal);
                const blobUrl = await fetchImageAsBlob(userProfilePhoto.url);
                setCropImage(blobUrl);
                setCropFileType('image/jpeg');
                setCropModalOpen(true);
              } else {
                // On desktop, use existing behavior
                setUsedUserProfileAsCutoutPhoto(true);
                const newPhotos = [...photos];
                // Update only posterPhotoWithoutBackgroundOriginal
                const index = newPhotos.findIndex(
                  (photo) => photo.type === UserPosterPhotoType.posterPhotoWithoutBackgroundOriginal
                );
                if (index >= 0) {
                  newPhotos[index] = {
                    type: UserPosterPhotoType.posterPhotoWithoutBackgroundOriginal,
                    photo: userProfilePhoto,
                    photo_file: null,
                  };
                } else {
                  newPhotos.push({
                    type: UserPosterPhotoType.posterPhotoWithoutBackgroundOriginal,
                    photo: userProfilePhoto,
                    photo_file: null,
                  });
                }
                setPhotos(newPhotos);
              }
            }}
          >
            Click Here
          </Button>
        </Grid>
      </Collapse>

      <Grid
        container
        spacing={6}
        alignItems="start"
        justifyContent="start"
      >
        {photoTypes.map((type) => {
          // Find the photo by its type (using type as primary key)
          const photoItem = photos.find((photo) => photo.type === type);
          const hasPhoto = !!(photoItem && (photoItem.photo || photoItem.photo_file));

          return (
            <Grid key={type}>
              {/* On mobile, override label click for all photo types */}
              {isMobile ? (
                <Box
                  role="button"
                  tabIndex={0}
                  onClick={() => {
                    setCurrentPhotoType(type);
                    setSelectModalOpen(true);
                  }}
                  style={{ cursor: 'pointer' }}>
                  <Grid container direction="column">
                    <Box
                      style={{
                        width: `${userPosterImageWidth}px`,
                        height: `${userPosterImageHeight}px`,
                        border: '1.5px #f0f0f0 solid',
                        borderRadius: '16px',
                        backgroundColor: '#FAF9F6',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        objectFit: 'fill',
                        overflow: 'hidden',
                        position: 'relative',
                      }}
                    >
                      {photoItem && photoItem.photo ? (
                        <Image
                          src={photoItem.photo.url || '/assets/upload_pic.png'}
                          alt="User Poster Photo"
                          fill
                          sizes="100%"
                          style={{ objectFit: 'cover' }}
                        />
                      ) : photoItem && photoItem.photo_file ? (
                        <Image
                          src={getBlobUrlForPhotoFile(photoItem.photo_file)}
                          alt="User Poster Photo"
                          fill
                          sizes="100%"
                          style={{ objectFit: 'cover' }}
                        />
                      ) : (
                        <Grid
                          container
                          direction="column"
                          alignItems="center"
                          spacing={1}
                        >
                          <Grid>
                            <Box
                              sx={{
                                backgroundColor: '#e6f7ff',
                                borderRadius: '50%',
                                padding: '16px',
                                color: '#1890ff',
                              }}
                            >
                              {getIconForPhotoTypes(type)}
                            </Box>
                          </Grid>
                          <Grid>
                            <Typography fontWeight="500" fontSize={14}>
                              {getCustomizedStringForPhotoTypes(type)}
                            </Typography>
                          </Grid>
                        </Grid>
                      )}
                    </Box>
                    {hasPhoto && (
                      <Typography sx={{ marginTop: '8px', textAlign: 'center' }} fontSize={16} fontWeight="500">
                        {getCustomizedStringForPhotoTypes(type)}
                      </Typography>
                    )}
                  </Grid>
                </Box>
              ) : (
                // Desktop or other types: default file input
                <>
                  <input
                    accept={acceptedFileTypes}
                    style={{ display: 'none' }}
                    id={`file-input-${type}`}
                    type="file"
                    onChange={(e) => handleFileChange(type, e)}
                  />
                  <label htmlFor={`file-input-${type}`} style={{ display: 'block', cursor: 'pointer' }}>
                    <Grid container direction="column">
                      <Box
                        style={{
                          width: `${userPosterImageWidth}px`,
                          height: `${userPosterImageHeight}px`,
                          border: '1.5px #f0f0f0 solid',
                          borderRadius: '16px',
                          backgroundColor: '#FAF9F6',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          objectFit: 'fill',
                          overflow: 'hidden',
                          position: 'relative',
                        }}
                      >
                        {photoItem && photoItem.photo ? (
                          <Image
                            src={photoItem.photo.url || '/assets/upload_pic.png'}
                            alt="User Poster Photo"
                            fill
                            sizes="100%"
                            style={{ objectFit: 'cover' }}
                          />
                        ) : photoItem && photoItem.photo_file ? (
                          <Image
                            src={getBlobUrlForPhotoFile(photoItem.photo_file)}
                            alt="User Poster Photo"
                            fill
                            sizes="100%"
                            style={{ objectFit: 'cover' }}
                          />
                        ) : (
                          <Grid
                            container
                            direction="column"
                            alignItems="center"
                            spacing={1}
                          >
                            <Grid>
                              <Box
                                sx={{
                                  backgroundColor: '#e6f7ff',
                                  borderRadius: '50%',
                                  padding: '16px',
                                  color: '#1890ff',
                                }}
                              >
                                {getIconForPhotoTypes(type)}
                              </Box>
                            </Grid>
                            <Grid>
                              <Typography fontWeight="500" fontSize={14}>
                                {getCustomizedStringForPhotoTypes(type)}
                              </Typography>
                            </Grid>
                          </Grid>
                        )}
                      </Box>
                      {hasPhoto && (
                        <Typography sx={{ marginTop: '8px', textAlign: 'center' }} fontSize={16} fontWeight="500">
                          {getCustomizedStringForPhotoTypes(type)}
                        </Typography>
                      )}
                    </Grid>
                  </label>
                </>
              )}
              {hasPhoto && (
                <Grid>
                  <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    marginTop={1}
                  >
                    {/* For posterPhotoWithoutBackgroundOriginal, use same logic as tile click */}
                    {isMobile ? (
                      <Button
                        startIcon={<PhotoCamera />}
                        variant="outlined"
                        sx={{ width: userPosterImageWidth }}
                        onClick={() => {
                          setCurrentPhotoType(type);
                          setSelectModalOpen(true);
                        }}
                      >
                        Change Photo
                      </Button>
                    ) : (
                      <>
                        <input
                          accept={acceptedFileTypes}
                          style={{ display: 'none' }}
                          id={`file-input-change-${type}`}
                          type="file"
                          onChange={(e) => handleFileChange(type, e)}
                        />
                        <label htmlFor={`file-input-change-${type}`}>
                          <Button
                            component="span"
                            startIcon={<PhotoCamera />}
                            variant="outlined"
                            sx={{ width: userPosterImageWidth }}
                          >
                            Change Photo
                          </Button>
                        </label>
                      </>
                    )}
                  </Grid>
                  <Grid sx={{ width: userPosterImageWidth, marginTop: 1 }}>
                    <Button
                      fullWidth
                      variant="outlined"
                      color="error"
                      startIcon={<DeleteRounded />}
                      onClick={() => handleRemove(type)}
                    >
                      Remove Photo
                    </Button>
                  </Grid>
                </Grid>
              )}
            </Grid>
          );
        })}
      </Grid>

      {/* Family Frame Name Field */}
      {!selectModalOpen && !cropModalOpen && !cropImage && (
        <Collapse
          in={familyPhotoExists}
          timeout={500}
        >
          <Grid
            container
            direction="column"
            spacing={2}
            sx={{ marginTop: 2 }}
          >
            <Grid>
              <TextField
                fullWidth
                label="Family Frame Name"
                placeholder="Enter Family Frame Name"
                variant="outlined"
                value={localFamilyFrameName}
                onChange={(e) => handleFamilyFrameNameChange(e.target.value)}
                type="text"
              />
            </Grid>
          </Grid>
        </Collapse>
      )}
    </Grid>
  );
}

export default UserPosterPhotosUi;
