'use client';

import Grid from '@mui/material/Grid2';
import { TextField, IconButton } from '@mui/material';
import { Dispatch, SetStateAction, useState } from 'react';
import { Mic } from '@mui/icons-material';
import { useVoiceRecorder } from '@/hooks/use-voice-recorder';
import VoiceRecordingDialog from './voice-recording-dialog';

interface BasicInfoUiProps {
  badgeText: string;
  setBadgeText: Dispatch<SetStateAction<string>>;
  existingBadgeDescription: string | null;
  referrerId: number | null;
  setReferrerId: Dispatch<SetStateAction<number | null>>;
  enableReferrerId: boolean;
}

function BasicInfoUi({
  badgeText,
  setBadgeText,
  existingBadgeDescription,
  referrerId,
  setReferrerId,
  enableReferrerId,
}: BasicInfoUiProps) {
  const [isRecordingDialogOpen, setIsRecordingDialogOpen] = useState(false);
  const { transcript, listening, errorMsg, startRecording, stopRecording, resetTranscript } =
    useVoiceRecorder('te-IN');

  const handleStartVoiceRecording = async () => {
    try {
      await startRecording();
      setIsRecordingDialogOpen(true);
    } catch (error) {
      console.error('Failed to start recording:', error);
      // errorMsg will be set by the hook
      alert(errorMsg || 'Failed to start recording. Please try again.');
    }
  };

  const cleanupRecording = () => {
    stopRecording().catch((error) => console.error('Error stopping recording:', error));
    setIsRecordingDialogOpen(false);
    resetTranscript();
  };

  return (
    <>
      <Grid
        container
        direction="column"
        spacing={2}
        justifyContent="center"
      >
        <Grid size="grow">
          <TextField
            fullWidth
            label="Badge Text"
            placeholder="Enter Badge Text Here"
            variant="outlined"
            value={badgeText}
            onChange={(e) => setBadgeText(e.target.value)}
            helperText={
              existingBadgeDescription ? `Existing Badge: ${existingBadgeDescription}` : null
            }
            slotProps={{
              input: {
                endAdornment: (
                  <IconButton
                    onClick={handleStartVoiceRecording}
                    disabled={listening}
                    edge="end"
                  >
                    <Mic />
                  </IconButton>
                ),
              },
              formHelperText: {
                sx: {
                  textAlign: 'left',
                  marginLeft: '0px',
                },
              },
            }}
            sx={{
              marginTop: '16px',
            }}
          />
        </Grid>

        {/* Referrer ID Field */}
        <Grid size="grow">
          <TextField
            fullWidth
            label="Referrer ID"
            placeholder="Enter Referrer ID"
            variant="outlined"
            value={referrerId || ''}
            onChange={(e) => {
              // Only allow numeric input
              const numericValue = e.target.value.replace(/[^0-9]/g, '');
              const value = numericValue === '' ? null : Number(numericValue);
              setReferrerId(value);
            }}
            disabled={!enableReferrerId}
            // Using type="text" instead of "number" to avoid browser's up/down arrow controls
            // while still restricting input to numbers only
            type="text"
            slotProps={{
              htmlInput: {
                // tells mobile browsers to show the numeric keypad
                inputMode: 'numeric',
                // a simple regex allowing only digits
                pattern: '[0-9]*',
              },
            }}
          />
        </Grid>
      </Grid>

      <VoiceRecordingDialog
        open={isRecordingDialogOpen}
        onClose={cleanupRecording}
        onUseText={(text) => {
          setBadgeText(text);
          cleanupRecording();
        }}
        transcript={transcript}
        listening={listening}
        errorMsg={errorMsg}
        onStartRecording={handleStartVoiceRecording}
      />
    </>
  );
}

export default BasicInfoUi;
