'use client';

import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  TextField,
  IconButton,
} from '@mui/material';
import { Mic } from '@mui/icons-material';
import { useState, useEffect } from 'react';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { useVoiceRecorder } from '@/hooks/use-voice-recorder';
import VoiceRecordingDialog from './voice-recording-dialog';

interface EditUserNameDialogProps {
  open: boolean;
  onClose: () => void;
  userName: string;
  setUserName: (name: string) => void;
  dob: string | null;
  setDob: (dob: string | null) => void;
  isDobPreFilled: boolean;
}

function EditUserNameDialog({
  open,
  onClose,
  userName,
  setUserName,
  dob,
  setDob,
  isDobPreFilled,
}: EditUserNameDialogProps) {
  const [tempUserName, setTempUserName] = useState(userName);
  const [tempDob, setTempDob] = useState(dob);
  const [isRecordingDialogOpen, setIsRecordingDialogOpen] = useState(false);
  const { transcript, listening, errorMsg, startRecording, stopRecording, resetTranscript } =
    useVoiceRecorder('te-IN');

  useEffect(() => {
    if (open) {
      setTempUserName(userName);
      setTempDob(dob);
    }
  }, [open, userName, dob]);

  const cleanupRecording = () => {
    stopRecording().catch((error) => console.error('Error stopping recording:', error));
    setIsRecordingDialogOpen(false);
    resetTranscript();
  };

  const handleStartVoiceRecording = async () => {
    try {
      await startRecording();
      setIsRecordingDialogOpen(true);
    } catch (error) {
      console.error('Failed to start recording:', error);
      // errorMsg will be set by the hook
      alert(errorMsg || 'Failed to start recording. Please try again.');
    }
  };

  const handleSave = () => {
    if (!tempUserName.trim()) {
      alert('Please enter a valid name before saving.');
      return;
    }
    setUserName(tempUserName);
    setDob(tempDob);
    onClose();
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
      >
        <DialogTitle>Edit Profile</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus={false}
            margin="dense"
            label="Name (Telugu)"
            type="text"
            fullWidth
            variant="outlined"
            value={tempUserName}
            onChange={(e) => setTempUserName(e.target.value)}
            slotProps={{
              input: {
                endAdornment: (
                  <IconButton
                    onClick={handleStartVoiceRecording}
                    disabled={listening}
                    edge="end"
                  >
                    <Mic />
                  </IconButton>
                ),
              },
            }}
            sx={{ mb: 2 }}
          />
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              value={tempDob ? dayjs(tempDob, 'YYYY-MM-DD') : null}
              label="Date of Birth"
              format="DD/MM/YYYY"
              disabled={isDobPreFilled}
              onChange={(date) => setTempDob(date ? date.format('YYYY-MM-DD') : null)}
              maxDate={dayjs().subtract(13, 'years')}
              slotProps={{
                textField: {
                  fullWidth: true,
                  margin: 'dense',
                  variant: 'outlined',
                },
              }}
            />
          </LocalizationProvider>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={onClose}
            variant="outlined"
            sx={{
              borderColor: '#000',
              color: '#000'
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={!tempUserName.trim()}
            variant="contained"
            sx={{
              backgroundColor: '#000',
              color: '#fff',
              '&:hover': {
                boxShadow: '0px 0px 10px rgba(0,0,0,0.5)'
              },
              '&:disabled': {
                backgroundColor: '#ccc',
                color: '#666'
              }
            }}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>

      <VoiceRecordingDialog
        open={isRecordingDialogOpen}
        onClose={cleanupRecording}
        onUseText={(text) => {
          setTempUserName(text);
          cleanupRecording();
        }}
        transcript={transcript}
        listening={listening}
        errorMsg={errorMsg}
        onStartRecording={handleStartVoiceRecording}
      />
    </>
  );
}

export default EditUserNameDialog;
