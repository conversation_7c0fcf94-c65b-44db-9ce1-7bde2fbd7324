import { Typography } from '@mui/material';

interface RemarkMessageUiProps {
  message: string;
}

function RemarkMessageUi({ message }: RemarkMessageUiProps) {
  return (
    <Typography
      variant="body2"
      fontWeight="500"
      color="red"
      sx={{
        padding: '8px',
        borderRadius: '5px',
        backgroundColor: '#f8d7da',
        boxShadow: '0px 0px 16px red',
      }}
    >
      Remarks: {message}
    </Typography>
  );
}

export default RemarkMessageUi;
