'use client';

import Grid from '@mui/material/Grid2';
import { Typo<PERSON>, Button, Container } from '@mui/material';
import UserProfileUi from '@/components/create-layout/user-profile-ui';
import BasicInfoUi from '@/components/create-layout/basic-info-ui';
import { LayoutCreationInfoResponse } from '@/types/layout-creation-info-response';
import { useCallback, useMemo, useRef, useState } from 'react';
import { CircleDetails } from '@/types/circle-details';
import LeaderProtocolCirclesUi from '@/components/create-layout/leader-protocol-circles-ui';
import {
  attachNanoIdToProtocolCircle,
  ProtocolCircleDetails,
} from '@/types/protocol-circle-details';
import UserPosterPhotosUi from '@/components/create-layout/user-poster-photos-ui';
import { UserPosterPhoto, UserPosterPhotoType } from '@/types/user-poster-photo';
import RemarkMessageUi from '@/components/create-layout/remark-message-ui';
import createLayoutService from '@/services/create-layout-service';
import { ApiException } from '@/exceptions/api-exception';
import { SubmitLayoutEnum } from '@/enums/submit-layout-enum';
import SubmitConfirmationDialog from '@/components/create-layout/submit-confirmation-dialog';

interface CreateLayoutUiProps {
  response: LayoutCreationInfoResponse;
}

enum SubmissionStateEnum {
  idle,
  submitting,
  success,
  error,
}

export interface SubmissionState {
  state: SubmissionStateEnum;
  message?: string;
}

function CreateLayoutUi({ response }: CreateLayoutUiProps) {
  const [badgeText, setBadgeText] = useState<string>(response.badge_free_text || '');
  const [selectedParty, setSelectedParty] = useState<CircleDetails | null>(
    response.user.affiliated_party || null
  );
  // No Affiliated Party Case
  const [selectedNoAffiliatedParty, setSelectedNoAffiliatedParty] = useState<boolean>(
    response.selected_no_affiliated_party || false
  );

  // Leader Protocol Circles
  const [circles, setCircles] = useState<ProtocolCircleDetails[]>(() =>
    (response.protocol_leader_circles || []).map(attachNanoIdToProtocolCircle)
  );

  //No Protocol Positions Found
  const [noPositionsFound, setNoPositionsFound] = useState<boolean>(false);

  // User Poster Photos
  const [userPosterPhotos, setUserPosterPhotos] = useState<UserPosterPhoto[]>([
    ...(response.user_poster_photos || []),
  ]);

  const [usedUserProfileAsCutoutPhoto, setUsedUserProfileAsCutoutPhoto] = useState<boolean>(false);

  const [userName, setUserName] = useState<string>(response.user.name || '');

  // Referrer ID
  const [referrerId, setReferrerId] = useState<number | null>(response.referrer_id || null);

  // Family Frame Name
  const [familyFrameName, setFamilyFrameName] = useState<string>(response.family_frame_name || '');

  // Dob
  const initialDob = response.user.dob;
  const [dob, setDob] = useState<string | null>(initialDob || null);
  const isDobPrefilled = !!initialDob;

  // Initial Snapshot
  const initialBadgeText = useRef(response.badge_free_text || '');
  const initialCircles = useRef(JSON.stringify(circles));
  const initialUserPosterPhotos = useRef(JSON.stringify(userPosterPhotos));

  const has_boe_update = useMemo(() => {
    return badgeText !== initialBadgeText.current;
  }, [badgeText]);

  const has_oe_update = useMemo(() => {
    const circlesChanged = JSON.stringify(circles) !== initialCircles.current;
    const userPosterPhotosChanged =
      JSON.stringify(userPosterPhotos) !== initialUserPosterPhotos.current;

    return circlesChanged || userPosterPhotosChanged;
  }, [circles, userPosterPhotos]);

  // Form Submission State
  const [submissionState, setSubmissionState] = useState<SubmissionState>({
    state: SubmissionStateEnum.idle,
  });

  // Submit Confirmation Dialog State
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState<boolean>(false);

  // Check if the confirmation dialog should be shown
  // Memoized function: Determines whether to show the confirmation dialog.
  const shouldShowConfirmationDialog = useCallback((): boolean => {
    const lastConfirmationDialogShownTime = localStorage.getItem(
      'submitConfirmationDialogConsentTime'
    );
    if (lastConfirmationDialogShownTime) {
      const lastTime = new Date(lastConfirmationDialogShownTime);
      if (isNaN(lastTime.getTime())) {
        return true;
      }
      const currentTime = new Date();
      // If the confirmation was shown today, do not show it again.
      if (lastTime.toDateString() === currentTime.toDateString()) {
        return false;
      }
    }
    return true;
  }, []);

  // Handles the actual submission after confirmation
  const handleSubmit = async (status: SubmitLayoutEnum = SubmitLayoutEnum.rmDraft) => {
    const data = {
      user_id: response.user.id,
      user_name: userName,
      status: status,
      badge_free_text: badgeText,
      selected_no_affiliated_party: selectedNoAffiliatedParty,
      dob: dob,
      poster_affiliated_party_id: selectedParty?.id ?? null,
      protocol_leader_circles: circles,
      user_poster_photos: userPosterPhotos,
      used_user_profile_as_cutout_photo: usedUserProfileAsCutoutPhoto,
      has_boe_update: has_boe_update,
      has_oe_update: has_oe_update,
      referrer_id: referrerId,
      family_frame_name: familyFrameName.trim() || null,
    };
    console.debug(data);

    setSubmissionState({ state: SubmissionStateEnum.submitting });

    const formData = new FormData();
    formData.append('user_id', JSON.stringify(response.user.id));
    formData.append('user_name', userName);
    formData.append('badge_free_text', badgeText);
    formData.append('status', status);
    formData.append('has_boe_update', JSON.stringify(has_boe_update));
    formData.append('has_oe_update', JSON.stringify(has_oe_update));
    if (dob && dob !== response.user.dob) {
      formData.append('dob', dob);
    }
    formData.append('selected_no_affiliated_party', JSON.stringify(selectedNoAffiliatedParty));
    if (selectedParty) {
      formData.append('poster_affiliated_party_id', JSON.stringify(selectedParty.id));
    }
    // Add referrer_id to form data if it exists and is enabled
    if (referrerId !== null) {
      formData.append('referrer_id', JSON.stringify(referrerId));
    }

    // Add family_frame_name to form data if provided
    if (familyFrameName.trim()) {
      formData.append('family_frame_name', familyFrameName);
    }
    formData.append(
      'used_user_profile_as_cutout_photo',
      JSON.stringify(usedUserProfileAsCutoutPhoto)
    );

    circles.forEach((circle, index) => {
      if (circle.id) {
        formData.append(`protocol_leader_circles[${index}][id]`, JSON.stringify(circle.id));
      }
      if (circle.photo_file instanceof File) {
        formData.append(`protocol_leader_circles[${index}][photo_file]`, circle.photo_file);
      }

      circle.poster_photos.forEach((photo, photoIndex) => {
        formData.append(
          `protocol_leader_circles[${index}][poster_photos][${photoIndex}][id]`,
          JSON.stringify(photo.id)
        );
        formData.append(
          `protocol_leader_circles[${index}][poster_photos][${photoIndex}][url]`,
          photo.url
        );
        formData.append(
          `protocol_leader_circles[${index}][poster_photos][${photoIndex}][placeholderUrl]`,
          photo.placeholderUrl || ''
        );
        formData.append(
          `protocol_leader_circles[${index}][poster_photos][${photoIndex}][selected]`,
          JSON.stringify(photo.selected)
        );
      });
      formData.append(
        `protocol_leader_circles[${index}][priority]`,
        JSON.stringify(circle.priority)
      );
      formData.append(`protocol_leader_circles[${index}][name]`, circle.name);
      if (circle.short_name) {
        formData.append(`protocol_leader_circles[${index}][short_name]`, circle.short_name);
      }
      if (circle.sub_text) {
        formData.append(`protocol_leader_circles[${index}][sub_text]`, circle.sub_text);
      }
      formData.append(`protocol_leader_circles[${index}][type]`, circle.type);
    });
    userPosterPhotos.forEach((userPosterPhoto, index) => {
      if (userPosterPhoto.photo_file instanceof File) {
        formData.append(`user_poster_photos[${index}][photo_file]`, userPosterPhoto.photo_file);
      }

      if (userPosterPhoto.photo) {
        formData.append(
          `user_poster_photos[${index}][photo]`,
          JSON.stringify(userPosterPhoto.photo)
        );
      }

      formData.append(`user_poster_photos[${index}][type]`, userPosterPhoto.type);
    });

    try {
      const savedResponse = await createLayoutService.saveLayoutAsDraft({ data: formData });
      setSubmissionState({
        state: SubmissionStateEnum.success,
        message:
          status === SubmitLayoutEnum.rmDraft
            ? 'Draft Data Saved Successfully!!!'
            : 'Layout Submitted Successfully!!!',
      });
      console.debug(savedResponse);
    } catch (error: unknown) {
      const defaultMessage = `An unexpected error occurred while ${status === SubmitLayoutEnum.rmDraft ? 'saving the draft' : 'submitting the layout'}.`;
      const message = ApiException.getErrorMessage(error, defaultMessage);
      setSubmissionState({ state: SubmissionStateEnum.error, message });
    }
  };

  const containsCutoutPhoto = useMemo(() => {
    return userPosterPhotos
      .map((photo) => photo.type)
      .includes(UserPosterPhotoType.posterPhotoWithoutBackgroundOriginal);
  }, [userPosterPhotos]);

  const allProtocolCirclesHavePhotos = useMemo(() => {
    return (
      circles.length === 0 ||
      circles.every(
        (circle) =>
          circle.photo_file instanceof File ||
          (circle.poster_photos && circle.poster_photos.length > 0)
      )
    );
  }, [circles]);

  const getSubmitUI = () => {
    switch (submissionState.state) {
      case SubmissionStateEnum.idle:
        return (
          <Grid
            container
            spacing={2}
            size="grow"
          >
            <Grid size={6}>
              <Button
                fullWidth
                variant="outlined"
                disabled={noPositionsFound}
                onClick={() => handleSubmit(SubmitLayoutEnum.rmDraft)}
                sx={{
                  padding: '8px',
                  width: '100%',
                  borderColor: '#000',
                  color: '#000',
                }}
              >
                Save Draft
              </Button>
            </Grid>
            <Grid size={6}>
              <Button
                fullWidth
                // we need to disable if selectedNoAffiliatedParty is false and selectedParty is null
                disabled={
                  !containsCutoutPhoto ||
                  noPositionsFound ||
                  !allProtocolCirclesHavePhotos ||
                  (!selectedNoAffiliatedParty && !selectedParty)
                }
                variant="contained"
                onClick={async () => {
                  if (shouldShowConfirmationDialog()) {
                    setIsConfirmationDialogOpen(true);
                  } else {
                    await handleSubmit(SubmitLayoutEnum.rmSubmitted);
                  }
                }}
                sx={{
                  padding: '8px',
                  width: '100%',
                  backgroundColor: '#000',
                  color: '#fff',
                  '&:hover': {
                    boxShadow: '0px 0px 10px rgba(0,0,0,0.5)',
                  },
                  '&:disabled': {
                    backgroundColor: '#ccc',
                    color: '#666',
                  },
                }}
              >
                Submit
              </Button>
            </Grid>
          </Grid>
        );
      case SubmissionStateEnum.submitting:
        return (
          <Grid
            container
            spacing={2}
            size="grow"
          >
            <Grid size={6}>
              <Button
                fullWidth
                variant="outlined"
                disabled={true}
                sx={{
                  padding: '8px',
                  width: '100%',
                  borderColor: '#ccc',
                  color: '#666',
                }}
              >
                Save Draft
              </Button>
            </Grid>
            <Grid size={6}>
              <Button
                fullWidth
                disabled={true}
                variant="contained"
                sx={{
                  padding: '8px',
                  width: '100%',
                  backgroundColor: '#ccc',
                  color: '#666',
                }}
              >
                Submitting...
              </Button>
            </Grid>
          </Grid>
        );

      case SubmissionStateEnum.error:
        return (
          <Grid
            container
            size="grow"
            justifyContent="center"
            spacing={2}
          >
            <Typography
              color="error.main"
              textAlign="center"
            >
              ❌ {submissionState.message}
            </Typography>
            <Button
              fullWidth
              variant="contained"
              onClick={() => setSubmissionState({ state: SubmissionStateEnum.idle })}
              sx={{
                padding: '8px',
                backgroundColor: '#000',
                color: '#fff',
                '&:hover': {
                  boxShadow: '0 0 10px rgba(0,0,0,0.5)',
                },
              }}
            >
              Try Again
            </Button>
          </Grid>
        );

      case SubmissionStateEnum.success:
        return (
          <Grid
            container
            size="grow"
            justifyContent="center"
            direction="column"
            alignItems="center"
            spacing={1}
          >
            <Typography
              color="success.main"
              textAlign="center"
              fontSize={18}
              fontWeight="bold"
            >
              🎉 {submissionState.message}
            </Typography>
            <Typography
              textAlign="center"
              fontSize={16}
              color="#666"
            >
              You can close the tab now.
            </Typography>
          </Grid>
        );

      default:
        return null;
    }
  };

  return (
    <Container
      fixed
      style={{ boxShadow: '0px 0px 10px 0px rgba(0,0,0,0.1)' }}
    >
      <Grid
        container
        direction="column"
        spacing={2}
        alignItems="flex-start"
      >
        <Grid>
          <Typography
            sx={{ mt: 4 }}
            variant="h3"
          >
            Layout Information
          </Typography>
        </Grid>
        {/* Remarks UI */}
        {response.remarks && response.remarks.length > 0 && (
          <Grid>
            <RemarkMessageUi message={response.remarks} />
          </Grid>
        )}

        <UserProfileUi
          user_details={response.user}
          selectedParty={selectedParty}
          setSelectedParty={setSelectedParty}
          dob={dob}
          setDob={setDob}
          isDobPreFilled={isDobPrefilled}
          userName={userName}
          setUserName={setUserName}
          selectedNoAffiliatedParty={selectedNoAffiliatedParty}
          setSelectedNoAffiliatedParty={setSelectedNoAffiliatedParty}
        />

        <Grid size={{ xs: 12, sm: 10, md: 8 }}>
          <BasicInfoUi
            badgeText={badgeText}
            setBadgeText={setBadgeText}
            existingBadgeDescription={response.user.badge?.description || null}
            referrerId={referrerId}
            setReferrerId={setReferrerId}
            enableReferrerId={response.enable_referrer_id || false}
          />
        </Grid>
        <Grid
          container
          size={12}
        >
          <LeaderProtocolCirclesUi
            circles={circles}
            setCircles={setCircles}
            userId={response.user.id}
            noPositionsFound={noPositionsFound}
            setNoPositionsFound={setNoPositionsFound}
            selectedParty={selectedParty}
            selectedNoAffiliatedParty={selectedNoAffiliatedParty}
          />
        </Grid>
        <Grid>
          <UserPosterPhotosUi
            photos={userPosterPhotos}
            setPhotos={setUserPosterPhotos}
            userProfilePhoto={response.user.photo}
            usedUserProfileAsCutoutPhoto={usedUserProfileAsCutoutPhoto}
            setUsedUserProfileAsCutoutPhoto={setUsedUserProfileAsCutoutPhoto}
            familyFrameName={familyFrameName}
            setFamilyFrameName={setFamilyFrameName}
          />
        </Grid>
        <Grid
          container
          size={12}
          sx={{ marginBottom: 4, marginTop: 4 }}
        >
          {getSubmitUI()}
        </Grid>
      </Grid>

      {/* Confirmation Dialog */}
      <SubmitConfirmationDialog
        open={isConfirmationDialogOpen}
        onClose={() => setIsConfirmationDialogOpen(false)}
        onCancel={() => setIsConfirmationDialogOpen(false)}
        onConfirm={async () => {
          // Save the confirmation time to localStorage
          localStorage.setItem('submitConfirmationDialogConsentTime', new Date().toISOString());
          setIsConfirmationDialogOpen(false);
          await handleSubmit(SubmitLayoutEnum.rmSubmitted);
        }}
      />
    </Container>
  );
}

export default CreateLayoutUi;
