'use client';

import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  CircularProgress,
  IconButton,
} from '@mui/material';
import { Mic } from '@mui/icons-material';

interface VoiceRecordingDialogProps {
  open: boolean;
  onClose: () => void;
  onUseText: (text: string) => void;
  transcript: string;
  listening: boolean;
  errorMsg: string;
  onStartRecording: () => void;
}

function VoiceRecordingDialog({
  open,
  onClose,
  onUseText,
  transcript,
  listening,
  errorMsg,
  onStartRecording,
}: VoiceRecordingDialogProps) {
  return (
    <Dialog
      open={open}
      onClose={onClose}
    >
      <DialogTitle>Recording in Progress</DialogTitle>
      <DialogContent>
        <Box
          sx={{
            p: 2,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 2,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: 1,
            }}
          >
            {listening ? (
              <CircularProgress color="primary" />
            ) : (
              <IconButton
                onClick={onStartRecording}
                sx={{
                  backgroundColor: '#000',
                  color: '#fff',
                  '&:hover': {
                    backgroundColor: '#000',
                    boxShadow: '0px 0px 10px rgba(0,0,0,0.5)'
                  }
                }}
                disabled={listening}
              >
                <Mic />
              </IconButton>
            )}
            <Typography color="primary">{listening ? 'Listening...' : 'Record again'}</Typography>
          </Box>
          {errorMsg && (
            <Typography
              variant="body2"
              color="error"
            >
              {errorMsg}
            </Typography>
          )}
          <Typography
            variant="body1"
            sx={{
              minHeight: '50px',
              maxWidth: '300px',
              wordWrap: 'break-word',
              textAlign: 'center',
              bgcolor: 'background.paper',
              p: 2,
              borderRadius: 1,
            }}
          >
            {transcript || 'Waiting for speech...'}
          </Typography>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{
            borderColor: '#000',
            color: '#000'
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={() => {
            if (transcript) {
              onUseText(transcript);
            }
          }}
          variant="contained"
          disabled={!transcript}
          sx={{
            backgroundColor: '#000',
            color: '#fff',
            '&:hover': {
              boxShadow: '0px 0px 10px rgba(0,0,0,0.5)'
            },
            '&:disabled': {
              backgroundColor: '#ccc',
              color: '#666'
            }
          }}
        >
          Use This Text
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default VoiceRecordingDialog;
