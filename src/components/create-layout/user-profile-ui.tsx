'use client';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Autocomplete,
  TextField,
  CircularProgress,
  IconButton,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import Image from 'next/image';
import { Home, WhatsApp, Edit } from '@mui/icons-material';
import { useState, useEffect, Dispatch, SetStateAction, useCallback } from 'react';
import createLayoutService from '@/services/create-layout-service';
import { UserDetails } from '@/types/user-details';
import { CircleDetails } from '@/types/circle-details';
import EditUserNameDialog from './edit-user-name-dialog';

interface UserProfileUiProps {
  user_details: UserDetails;
  selectedParty: CircleDetails | null;
  setSelectedParty: Dispatch<SetStateAction<CircleDetails | null>>;
  dob: string | null;
  setDob: Dispatch<SetStateAction<string | null>>;
  isDobPreFilled: boolean;
  userName: string;
  setUserName: Dispatch<SetStateAction<string>>;
  selectedNoAffiliatedParty: boolean;
  setSelectedNoAffiliatedParty: Dispatch<SetStateAction<boolean>>;
}

const _userPhotoSize = 200;

function UserProfileUi({
  user_details,
  selectedParty,
  setSelectedParty,
  dob,
  setDob,
  isDobPreFilled,
  userName,
  setUserName,
  selectedNoAffiliatedParty,
  setSelectedNoAffiliatedParty,
}: UserProfileUiProps) {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [options, setOptions] = useState<CircleDetails[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [openEditDialog, setOpenEditDialog] = useState<boolean>(false);

  const fetchData = useCallback(async () => {
    if (searchTerm.length <= 2) return setOptions([]);
    setLoading(true);
    try {
      const data = await createLayoutService.fetchPartyCircles({ searchTerm });
      setOptions(data);
    } catch (error) {
      console.error('Error fetching parties:', error);
    } finally {
      setLoading(false);
    }
  }, [searchTerm]);

  useEffect(() => {
    const timeoutId = setTimeout(fetchData, 500);
    return () => clearTimeout(timeoutId);
  }, [fetchData]);

  return (
    <>
      <Grid
        container
        spacing={2}
      >
        {/* User Profile Photo and Contact Buttons */}
        <Grid
          container
          direction="column"
          spacing={1}
        >
          <Grid>
            {user_details.photo?.url ? (
              <Image
                src={user_details.photo.url}
                alt="User Profile Photo"
                width={_userPhotoSize}
                height={_userPhotoSize}
                style={{ borderRadius: '16px' }}
                priority
              />
            ) : (
              <Box
                sx={{
                  width: _userPhotoSize,
                  height: _userPhotoSize,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#f0f0f0',
                  borderRadius: '16px',
                }}
              >
                <Typography
                  fontSize={48}
                  fontWeight="bold"
                  color="#666"
                >
                  {user_details.name[0]}
                </Typography>
              </Box>
            )}
          </Grid>
          <Grid
            container
            direction="row"
          >
            <Button
              fullWidth
              variant="outlined"
              onClick={() => window.open(`https://wa.me/${user_details.phone}`, '_blank')}
              sx={{
                height: 32,
                color: '#25d366',
                borderColor: '#25d366',
                textTransform: 'none',
              }}
            >
              <WhatsApp sx={{ mr: 1 }} />
              Chat on WhatsApp
            </Button>
          </Grid>
        </Grid>

        {/* User Details and Editable Party Autocomplete */}
        <Grid
          container
          direction="column"
          spacing={1}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography
              fontSize={32}
              fontWeight="bold"
            >
              {userName}
            </Typography>
            <IconButton
              onClick={() => setOpenEditDialog(true)}
              size="small"
              sx={{ ml: 1 }}
            >
              <Edit />
            </IconButton>
          </Box>
          {user_details.badge?.description && (
            <Typography
              fontSize={14}
              fontWeight="500"
              color="#666"
            >
              {user_details.badge.description}
            </Typography>
          )}
          <Grid
            container
            spacing={1}
            direction="row"
            wrap="nowrap"
          >
            <Home />
            <Typography
              fontSize={14}
              fontWeight={500}
              textAlign="left"
            >
              {`${user_details.village.name}, ${user_details.mandal.name} Mandal, ${user_details.district.name} District, ${user_details.state.name}`}
            </Typography>
          </Grid>
          {user_details.profession && user_details.profession.length > 0 && (
            <Grid
              container
              spacing={1}
              alignItems="center"
            >
              <Typography
                fontSize={14}
                fontWeight="bold"
                color="#666"
              >
                Profession:
              </Typography>
              <Typography
                fontSize={16}
                fontWeight="bold"
              >
                {user_details.profession}
              </Typography>
            </Grid>
          )}
          {/* Select Affiliated Party */}
          <Grid container>
            <Autocomplete
              size="small"
              sx={{ marginTop: '8px' }}
              fullWidth
              options={options}
              getOptionLabel={(option) => option.name}
              filterOptions={(options) => options}
              inputValue={searchTerm}
              value={selectedParty}
              onInputChange={(_, newInputValue) => setSearchTerm(newInputValue)}
              onChange={(_, newValue) => setSelectedParty(newValue)}
              loading={loading}
              disabled={selectedNoAffiliatedParty}
              noOptionsText="No Results Found"
              renderOption={(props, option) => (
                <li
                  {...props}
                  key={option.id}
                >
                  {option.id} - {option.short_name ? `${option.short_name} - ` : ''}
                  {option.name} {option.name_en ? `(${option.name_en})` : ''}
                </li>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Affiliated Party"
                  slotProps={{
                    input: {
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {loading ? (
                            <CircularProgress
                              color="inherit"
                              size={20}
                            />
                          ) : null}
                          {params.InputProps.endAdornment}
                        </>
                      ),
                    },
                  }}
                />
              )}
            />
            {/*Checkbox to select "No Affiliated Party"*/}
            <Grid
              container
              spacing={1}
              alignItems="center"
            >
              <input
                type="checkbox"
                checked={selectedNoAffiliatedParty}
                disabled={!!selectedParty}
                onChange={(e) => {
                  setSelectedNoAffiliatedParty(e.target.checked);
                  if (e.target.checked) {
                    setSelectedParty(null);
                  }
                }}
              />
              <Typography
                fontSize={14}
                fontWeight="bold"
                color="#666"
              >
                No Party Affiliation
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      <EditUserNameDialog
        open={openEditDialog}
        onClose={() => setOpenEditDialog(false)}
        userName={userName}
        setUserName={setUserName}
        dob={dob}
        setDob={setDob}
        isDobPreFilled={isDobPreFilled}
      />
    </>
  );
}

export default UserProfileUi;
