import { Dialog, DialogTitle, DialogContent, <PERSON>alogA<PERSON>, Button } from '@mui/material';
import React from 'react';

interface MobilePhotoSelectionModalProps {
  open: boolean;
  onClose: () => void;
  onCamera: () => void;
  onGallery: () => void;
}

const MobilePhotoSelectionModal: React.FC<MobilePhotoSelectionModalProps> = ({
  open,
  onClose,
  onCamera,
  onGallery
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      aria-labelledby="photo-selection-title"
      aria-describedby="photo-selection-description"
    >
      <DialogTitle id="photo-selection-title">Select Photo Source</DialogTitle>
      <DialogContent>
        <div id="photo-selection-description" className="sr-only">
          Choose between taking a new photo or selecting from your gallery
        </div>
        <Button
          fullWidth
          variant="contained"
          sx={{ my: 1 }}
          onClick={onCamera}
          aria-label="Take a new photo using camera"
        >
          Take Photo
        </Button>
        <Button
          fullWidth
          variant="outlined"
          sx={{ my: 1 }}
          onClick={onGallery}
          aria-label="Choose existing photo from gallery"
        >
          Choose from Gallery
        </Button>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} aria-label="Cancel photo selection">
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MobilePhotoSelectionModal;
