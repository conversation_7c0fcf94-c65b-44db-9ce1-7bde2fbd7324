import { Avatar, Box, FormControlLabel, Radio, Typography } from '@mui/material';
import { DragIndicator, Delete } from '@mui/icons-material';
import { ProtocolCircleDetails } from '@/types/protocol-circle-details';
import { getHeaderPhotoTypeShortValue, HeaderPhotoType } from '@/enums/header-photo-type';
import { Photo } from '@/types/photo';
import { FC, useMemo, useEffect } from 'react';
import Grid from '@mui/material/Grid2';

interface ProtocolCircleTileUiProps {
  circle: ProtocolCircleDetails;
  onPosterPhotoSelected: (id: number | string, type: Photo | null) => void;
  onHeaderTypeSelect: (id: number | string, type: HeaderPhotoType) => void;
  dragHandleProps?: Record<string, unknown>; // Props for the drag handle
  onCircleDelete: (circle: ProtocolCircleDetails) => void;
}

const ProtocolCircleTileUi: FC<ProtocolCircleTileUiProps> = ({
  circle,
  onPosterPhotoSelected,
  onHeaderTypeSelect,
  dragHandleProps,
  onCircleDelete,
}) => {
  const isRequested = !circle.id;
  const uniqueId = circle.nano_id || (circle.id ?? circle.name); // Ensure unique identifier for updates

  const objectUrl = useMemo(
    () => circle.photo_file ? URL.createObjectURL(circle.photo_file) : undefined,
    [circle.photo_file]
  );

  useEffect(() => {
    return () => {
      if (objectUrl) URL.revokeObjectURL(objectUrl);
    };
  }, [objectUrl]);

  return (
    <Grid
      container
      direction="row"
      alignItems="center"
      gap={1}
      sx={{
        padding: 1,
        borderRadius: 2,
        backgroundColor: '#f8f9fa',
        boxShadow: '0px 2px 5px rgba(0,0,0,0.1)',
        position: 'relative',
        '&:hover': { backgroundColor: '#e9ecef' },
      }}
    >
      {/* Column 1: Drag indicator + P:1 */}
      <Grid size={1} container direction="column" justifyContent="left" alignItems="center">
        <DragIndicator
          {...dragHandleProps}
          sx={{
            cursor: 'grab',
            color: 'gray',
            marginBottom: 2,
            marginLeft: -1,
            '&:active': {
              cursor: 'grabbing'
            }
          }}
        />
        <Box
          sx={{
            padding: '2px 10px',
            height: 'auto',
            minHeight: 'auto',
            width: 'auto',
            fontSize: '10px',
            lineHeight: '12px',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#e0e0e0',
            borderRadius: '16px',
            transform: 'rotate(-90deg)',
            transformOrigin: 'center center',
            marginTop: 0.5,
            marginLeft: -1,
            whiteSpace: 'nowrap',
            color: 'rgba(0, 0, 0, 0.87)',
          }}
        >
          {`P : ${circle.priority}`}
        </Box>
      </Grid>

      {/* Column 2: Protocol Photos + Header Type (responsive) */}
      <Grid size={10} sx={{ paddingRight: 2.5 }}>
        <Grid container direction="column" spacing={2}>
          {/* Protocol Photos */}
          <Grid size={12}>
            <Typography fontWeight="bold" fontSize={18} letterSpacing={0.25}>
              {circle.name}
            </Typography>
            <Grid container direction="row" alignItems="center">
              <Typography fontSize={14} color="#666" fontWeight="500">
                {circle.sub_text}
              </Typography>
            </Grid>
            {isRequested && (
              <Typography color="red" fontSize={12} fontWeight="500">
                Requested
              </Typography>
            )}
            {/* Poster Photos Selection */}
            <Grid container direction="column" gap={0.5} sx={{ marginTop: 2 }}>
              <Typography fontSize={14} fontWeight="500">
                Protocol Photos:
              </Typography>
              <Box
                sx={{
                  maxHeight: '180px',
                  overflowY: 'auto',
                  mb: 1,
                }}
              >
                <Grid container direction="row" alignItems="center" flexWrap="wrap">
                  {circle.poster_photos.map((photo) => (
                    <Box
                      key={photo.url}
                      onMouseDown={() => onPosterPhotoSelected(uniqueId, photo)}
                      sx={{
                        borderRadius: '50%',
                        cursor: 'pointer',
                        marginLeft: 1,
                        marginTop: 1,
                        marginBottom: 1,
                        boxShadow: photo.selected ? '0 0 7px 2px blue' : 'none',
                      }}
                    >
                      <Avatar
                        src={photo.url}
                        sx={{ width: 40, height: 40, cursor: 'pointer' }}
                      />
                    </Box>
                  ))}
                  {circle.poster_photos.length === 0 && (
                    <Box
                      onMouseDown={() => onPosterPhotoSelected(uniqueId, null)}
                      sx={{
                        borderRadius: '50%',
                        cursor: 'pointer',
                        marginLeft: 1,
                        marginTop: 1,
                        marginBottom: 1,
                        boxShadow: circle.photo_file ? '0 0 7px 2px blue' : 'none',
                      }}
                    >
                      <Avatar
                        src={objectUrl ?? '/assets/upload_pic.png'}
                        sx={{ width: 40, height: 40 }}
                      />
                    </Box>
                  )}
                </Grid>
              </Box>
            </Grid>
          </Grid>

          {/* Header Photo Type Selection */}
          <Grid size={12} container direction={{ xs: 'column', sm: 'row' }} alignItems="center">
            <Typography fontSize={12} fontWeight="500">
              Header Type:
            </Typography>
            <Grid
              container
              direction="row"
              alignItems="flex-start"
              sx={{
                marginTop: {
                  xs: -2,
                  sm: 0
                }
              }}
            >
              {Object.values(HeaderPhotoType).map((type) => (
                <FormControlLabel
                  key={type}
                  value={type}
                  slotProps={{
                    typography: {
                      fontSize: '14px'
                    }
                  }}
                  control={
                    <Radio
                      checked={circle.type === type}
                      onMouseDown={() => {
                        onHeaderTypeSelect(uniqueId, type);
                      }}
                      size="medium"
                    />
                  }
                  label={getHeaderPhotoTypeShortValue(type)}
                  sx={{
                    margin: 0,
                    marginRight: 2,
                  }}
                />
              ))}
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      {/* Delete Button - Absolute positioned in top right */}
      <Delete
        sx={{
          cursor: 'pointer',
          color: 'red',
          position: 'absolute',
          top: 8,
          right: 8,
          zIndex: 1,
          fontSize: 20
        }}
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          onCircleDelete(circle);
        }}
      />
    </Grid>
  );
};

export default ProtocolCircleTileUi;
