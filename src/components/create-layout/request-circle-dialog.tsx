import React, { <PERSON><PERSON><PERSON>, <PERSON>, SetStateAction } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Container,
  Box,
} from '@mui/material';
import Image from 'next/image';
import Grid from '@mui/material/Grid2';

interface RequestCircleDialogProps {
  open: boolean;
  requestedCircleName: string;
  setRequestedCircleName: Dispatch<SetStateAction<string>>;
  requestedCirclePhoto: File | null;
  setRequestedCirclePhoto: Dispatch<SetStateAction<File | null>>;
  onClose: () => void;
  onSubmit: () => void;
}

const RequestNewCircleDialog: FC<RequestCircleDialogProps> = ({
  open,
  onClose,
  onSubmit,
  requestedCircleName,
  setRequestedCircleName,
  requestedCirclePhoto,
  setRequestedCirclePhoto,
}) => {
  return (
    <Container>
      <Grid size={{ xs: 10, md: 6 }}>
        <Dialog
          fullWidth
          open={open}
          onClose={onClose}
        >
          <DialogTitle>Request New Circle</DialogTitle>
          <DialogContent>
            <Grid
              container
              direction="column"
              spacing={2}
            >
              <TextField
                label="Circle Name"
                fullWidth
                value={requestedCircleName}
                onChange={(e) => setRequestedCircleName(e.target.value)}
                margin="dense"
                required
                error={requestedCircleName.trim().length === 0}
                helperText={requestedCircleName.trim().length === 0 ? 'Name is required' : ''}
              />
              <input
                type="file"
                id="icon-button-file"
                style={{ display: 'none' }}
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    setRequestedCirclePhoto(file);
                  }
                }}
              />
              <label htmlFor="icon-button-file">
                <Grid
                  container
                  direction="row"
                  alignItems="center"
                  spacing={2}
                >
                  <Typography>
                    {requestedCirclePhoto ? 'Change' : 'Select'} Circle Photo:{' '}
                  </Typography>
                  <Button
                    variant="contained"
                    component="span"
                    sx={{
                      backgroundColor: '#000',
                      color: '#fff',
                      '&:hover': {
                        boxShadow: '0px 0px 10px rgba(0,0,0,0.5)'
                      }
                    }}
                  >
                    Upload
                  </Button>
                </Grid>
              </label>
              <Box
                sx={{
                  height: 150,
                  width: 150,
                  backgroundColor: '#FAF9F6',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  border: '1.5px #f0f0f0 solid',
                  borderRadius: '16px',
                  overflow: 'hidden',
                  objectFit: 'fill',
                  position: 'relative',
                }}
              >
                {requestedCirclePhoto ? (
                  <Image
                    src={URL.createObjectURL(requestedCirclePhoto)}
                    alt="Selected"
                    fill
                    sizes="100%"
                    style={{ objectFit: 'cover' }}
                  />
                ) : (
                  <Typography
                    variant="body2"
                    color="textSecondary"
                  >
                    Photo Preview
                  </Typography>
                )}
              </Box>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={onClose}
              variant="outlined"
              sx={{
                borderColor: '#000',
                color: '#000'
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                onSubmit();
              }}
              variant="contained"
              disabled={requestedCircleName.trim().length === 0}
              sx={{
                backgroundColor: '#000',
                color: '#fff',
                '&:hover': {
                  boxShadow: '0px 0px 10px rgba(0,0,0,0.5)'
                },
                '&:disabled': {
                  backgroundColor: '#ccc',
                  color: '#666'
                }
              }}
            >
              Submit
            </Button>
          </DialogActions>
        </Dialog>
      </Grid>
    </Container>
  );
};

export default RequestNewCircleDialog;
