'use client';

import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
} from '@mui/material';

interface SubmitConfirmationDialogProps {
  open: boolean;
  onClose: () => void;
  onCancel: () => void;
  onConfirm: () => void;
}

function SubmitConfirmationDialog({
  open,
  onClose,
  onCancel,
  onConfirm,
}: SubmitConfirmationDialogProps) {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>Confirmation</DialogTitle>
      <DialogContent>
        <Typography variant="body1">
          {`Once the layout is submitted, you cannot edit till <PERSON><PERSON> reviews it. Do you want to continue submitting?`}
        </Typography>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={onCancel}
          variant="outlined"
          sx={{
            borderColor: '#000',
            color: '#000',
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={onConfirm}
          variant="contained"
          sx={{
            backgroundColor: '#000',
            color: '#fff',
            '&:hover': {
              boxShadow: '0px 0px 10px rgba(0,0,0,0.5)',
            },
          }}
        >
          {`Yes, don't show again`}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default SubmitConfirmationDialog;
