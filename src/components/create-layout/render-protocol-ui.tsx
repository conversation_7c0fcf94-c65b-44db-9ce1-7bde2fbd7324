'use client';

import { Box, CircularProgress, Typography } from '@mui/material';
import Grid from '@mui/material/Grid2';
import Image from 'next/image';
import { useEffect, useMemo, useState } from 'react';
import { HeaderPhotoType } from '@/enums/header-photo-type';
import { ProtocolPhotoDetails } from '@/types/protocol-photo-details';
import { ProtocolCircleDetails } from '@/types/protocol-circle-details';

const posterWidthNum: number = 630;

interface RenderProtocolUiProps {
  noPositionsFound: boolean;
  protocolPhotoPositions: ProtocolPhotoDetails[];
  circles: ProtocolCircleDetails[];
  showLoading: boolean;
}

function RenderProtocolUi({
  noPositionsFound,
  protocolPhotoPositions,
  circles,
  showLoading,
}: RenderProtocolUiProps) {
  const calculateSizeAndPosition = (radius: number, positionX: number, positionY: number) => {
    const sizePx = radius;
    const posXPercent = (positionX / posterWidthNum) * 100; //Backend sending the width based on Posters Width
    const posYPercent = (positionY / 150) * 100;

    return { sizePx, posXPercent, posYPercent };
  };

  const positionTracker = new Map();

  const groupedPositions = useMemo(
    () =>
      protocolPhotoPositions.reduce<Record<HeaderPhotoType, ProtocolPhotoDetails[]>>(
        (acc, position) => {
          if (!acc[position.type]) acc[position.type] = [];
          acc[position.type].push(position);
          return acc;
        },
        {} as Record<HeaderPhotoType, ProtocolPhotoDetails[]>
      ),
    [protocolPhotoPositions]
  );

  const h1Count = circles.filter((circle) => circle.type === HeaderPhotoType.header1).length;
  const h2Count = circles.filter((circle) => circle.type === HeaderPhotoType.header2).length;

  function useDeviceWidth() {
    const [width, setWidth] = useState(0);

    useEffect(() => {
      setWidth(window.innerWidth);
      const handleResize = () => setWidth(window.innerWidth);
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    return width;
  }

  return (
    <Grid
      container
      direction="column"
      spacing={2}
    >
      <Typography
        variant="h6"
        sx={{
          paddingX: { xs: 1, sm: 1, md: 0 },
        }}
      >
        Protocol Preview
      </Typography>
      <Grid>
        <Box
          width={useDeviceWidth() >= 440 ? 440 : '100%'}
          height="150px"
          sx={{
            position: 'relative',
            backgroundColor: noPositionsFound ? '#f8d7da' : '#e0e0e0',
            borderRadius: '10px',
            boxShadow: noPositionsFound ? '0px 0px 16px red' : '0px 0px 5px rgba(0,0,0,0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: noPositionsFound ? 'center' : 'flex-start',
          }}
        >
          {showLoading ? (
            <Grid
              container
              justifyContent="center"
              alignItems="center"
              sx={{
                position: 'absolute',
                width: '100%',
                height: '100%',
                zIndex: 1,
                borderRadius: '10px',
                color: '#666',
              }}
            >
              <CircularProgress color="inherit" />
            </Grid>
          ) : (
            <>
              {noPositionsFound ? (
                <Typography
                  textAlign="center"
                  fontSize={14}
                  fontWeight="500"
                >
                  Not Supported Configuration
                  <Typography
                    display="block"
                    fontSize={16}
                    fontWeight="bold"
                  >
                    H1: {h1Count} and H2: {h2Count}
                  </Typography>
                </Typography>
              ) : (
                <>
                  {circles.map((circle, index) => {
                    const protocolHeaderType = circle.type;
                    const selectedPhoto = circle.poster_photos.find((photo) => photo.selected);
                    const photoFile = circle.photo_file;

                    // Getting the next available position for header type
                    if (!positionTracker.has(protocolHeaderType)) {
                      positionTracker.set(protocolHeaderType, 0);
                    }
                    const positionIndex = positionTracker.get(protocolHeaderType);
                    const protocolPosition = groupedPositions[protocolHeaderType]?.[positionIndex];

                    // Now let's move to next position
                    positionTracker.set(protocolHeaderType, positionIndex + 1);

                    if ((!selectedPhoto && !photoFile) || !protocolPosition) return null;

                    const { sizePx, posXPercent, posYPercent } = calculateSizeAndPosition(
                      protocolPosition.radius,
                      protocolPosition.position_x,
                      protocolPosition.position_y
                    );

                    return (
                      <Box
                        key={index}
                        sx={{
                          position: 'absolute',
                          height: sizePx,
                          width: sizePx,
                          left: `${posXPercent}%`,
                          top: `${posYPercent}%`,
                          borderRadius: '50%',
                          overflow: 'hidden',
                          animation: 'fadeIn 0.5s',
                          transition: 'all 0.5s',
                        }}
                      >
                        <Image
                          src={
                            photoFile
                              ? URL.createObjectURL(photoFile)
                              : (selectedPhoto?.url ?? '/assets/upload_pic.png')
                          }
                          alt="Circle Photo File"
                          fill
                          sizes="100%"
                          style={{
                            objectFit: 'cover',
                          }}
                        />
                      </Box>
                    );
                  })}
                </>
              )}
            </>
          )}
        </Box>
      </Grid>
    </Grid>
  );
}

export default RenderProtocolUi;
