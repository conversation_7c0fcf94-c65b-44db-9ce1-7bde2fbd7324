import { useSortable } from '@dnd-kit/sortable';
import { ReactNode, cloneElement, isValidElement, ReactElement, Children } from 'react';

interface SortableItemProps {
  id: number | string;
  children: ReactNode;
}



const SortableItem = ({ id, children }: SortableItemProps) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id });

  const style = {
    transform: transform ? `translateY(${transform.y}px)` : undefined,
    transition,
    touchAction: 'none',
  };

  // Helper function to safely clone a child and add drag handle props
  const cloneChildWithDragProps = (child: ReactNode): ReactNode => {
    if (isValidElement(child)) {
      return cloneElement(child as ReactElement<{ dragHandleProps?: Record<string, unknown> }>, {
        dragHandleProps: { ...attributes, ...listeners }
      });
    }
    return child;
  };

  // Pass drag handle props to children, handling various children types
  const childrenWithProps = (() => {
    // Handle single valid React element
    if (isValidElement(children)) {
      return cloneChildWithDragProps(children);
    }

    // Handle array of children or fragments (multiple children)
    if (Array.isArray(children) || Children.count(children) > 1) {
      return Children.map(children, cloneChildWithDragProps);
    }

    // Handle other types (strings, numbers, null, undefined, etc.)
    return children;
  })();

  return (
    <div
      ref={setNodeRef}
      style={style}
    >
      {childrenWithProps}
    </div>
  );
};

export default SortableItem;
