import React from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Box, Typography, Slider } from '@mui/material';
import <PERSON><PERSON>per from 'react-easy-crop';
import { CropArea } from '@/hooks/use-image-cropping';

interface ImageCroppingModalProps {
  open: boolean;
  image: string | null;
  crop: { x: number; y: number };
  setCrop: (crop: { x: number; y: number }) => void;
  zoom: number;
  setZoom: (zoom: number) => void;
  aspect: number;
  onCropComplete: (croppedArea: CropArea, croppedAreaPixels: CropArea) => void;
  onClose: () => void;
  onSave: () => void;
}

const ImageCroppingModal: React.FC<ImageCroppingModalProps> = ({
  open,
  image,
  crop,
  setCrop,
  zoom,
  setZoom,
  aspect,
  onCropComplete,
  onClose,
  onSave,
}) => (
  <Dialog open={open} onClose={onClose} maxWidth="xs" fullWidth>
    <DialogTitle>Crop Photo</DialogTitle>
    <DialogContent>
      {image && (
        <Box sx={{ position: 'relative', width: '100%', height: 350, background: '#333' }}>
          <Cropper
            image={image}
            crop={crop}
            zoom={zoom}
            aspect={aspect}
            cropShape="rect"
            showGrid={true}
            onCropChange={setCrop}
            onZoomChange={setZoom}
            onCropComplete={onCropComplete}
            minZoom={1}
            maxZoom={3}
          />
        </Box>
      )}
      <Box sx={{ mt: 2 }}>
        <Typography gutterBottom>Zoom</Typography>
        <Slider
          value={zoom}
          min={1}
          max={3}
          step={0.1}
          onChange={(_, value) => {
            if (typeof value === 'number' && !isNaN(value)) setZoom(value);
          }}
        />
      </Box>
    </DialogContent>
    <DialogActions>
      <Button onClick={onClose}>Cancel</Button>
      <Button variant="contained" onClick={onSave}>Save</Button>
    </DialogActions>
  </Dialog>
);

export default ImageCroppingModal;
