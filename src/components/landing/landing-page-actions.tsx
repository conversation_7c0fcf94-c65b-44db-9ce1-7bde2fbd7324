'use client';

import React, { useState, FC } from 'react';
import { TextField, Button, Stack, Paper, CircularProgress } from '@mui/material';
import Grid from '@mui/material/Grid2';
import { DashboardLinkResponse } from '@/types/dashboard-link-response';
import createLayoutService from '@/services/create-layout-service';
import { ApiException } from '@/exceptions/api-exception';
import { environmentConfig } from '@/config/environment';

// Component: LandingPageActions
const LandingPageActions: FC = () => {
  const [userId, setUserId] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  const buttonStyle = {
    backgroundColor: '#000',
    color: '#fff',
    '&:hover': {
      boxShadow: '0px 0px 10px rgba(0,0,0,0.5)',
    },
  };

  const handleNavigate = async (type: 'rm' | 'boe' | 'oe') => {
    if (!userId) return;
    setLoading(true);
    try {
      let redirectUrl: string;

      if (type === 'rm') {
        redirectUrl = `${environmentConfig.appUrl}/create-layout?user_id=${encodeURIComponent(
          userId
        )}`;
      } else {
        const response: DashboardLinkResponse = await createLayoutService.getDashboardLink({
          user_id: userId,
          type,
        });
        redirectUrl = response.redirect_url;
      }

      window.open(redirectUrl, '_blank');
    } catch (error) {
      console.error('Failed to fetch redirect URL:', error);
      const message = ApiException.getErrorMessage(
        error,
        'Failed to fetch redirect URL in LandingPageActions'
      );
      alert(message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Grid size={6}>
      <Paper
        elevation={6}
        sx={{
          p: 4,
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          width: '100%',
          borderRadius: 2,
          mx: 'auto',
        }}
      >
        <TextField
          label="User ID"
          variant="outlined"
          fullWidth
          value={userId}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setUserId(e.target.value)}
          margin="normal"
          disabled={loading}
        />
        <Stack
          spacing={2}
          direction="column"
          mt={2}
        >
          {(['rm', 'boe', 'oe'] as const).map((path) => (
            <Button
              key={path}
              variant="contained"
              sx={buttonStyle}
              fullWidth
              disabled={!userId || loading}
              onClick={() => handleNavigate(path)}
              startIcon={loading ? <CircularProgress size={16} /> : undefined}
            >
              {path.toUpperCase()} Dashboard Link
            </Button>
          ))}
        </Stack>
      </Paper>
    </Grid>
  );
};

export default LandingPageActions;
