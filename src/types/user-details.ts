import { z } from 'zod';
import { PhotoSchema } from '@/types/photo';
import { BadgeSchema } from '@/types/badge';
import { CircleDetailsSchema } from '@/types/circle-details';

export const UserDetailsSchema = z.object({
  id: z.number(),
  phone: z.number(),
  name: z.string(),
  photo: PhotoSchema.nullable().optional(),
  village: CircleDetailsSchema,
  mandal: CircleDetailsSchema,
  district: CircleDetailsSchema,
  state: CircleDetailsSchema,
  badge: BadgeSchema.nullable().optional(),
  affiliated_party: CircleDetailsSchema.nullable().optional(),
  profession: z.string().optional().nullable().default(''),
  dob: z.string().nullable().optional(),
});

export type UserDetails = z.infer<typeof UserDetailsSchema>;
