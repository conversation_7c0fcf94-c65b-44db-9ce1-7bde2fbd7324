import { z } from 'zod';
import { PhotoSchema } from '@/types/photo';

export const CircleDetailsSchema = z.object({
  id: z.number(),
  name: z.string(),
  name_en: z.string(),
  short_name: z.string().optional().nullable(),
  poster_photos: z.array(PhotoSchema).default([]),
  sub_text: z.string().nullable().optional().default(''),
});

export type CircleDetails = z.infer<typeof CircleDetailsSchema>;
