import { UserDetailsSchema } from '@/types/user-details';
import { ProtocolCircleDetailsSchema } from '@/types/protocol-circle-details';
import { UserPosterPhotoSchema } from '@/types/user-poster-photo';
import { z } from 'zod';

export const LayoutCreationInfoResponseSchema = z.object({
  user: UserDetailsSchema,
  remarks: z.string().optional().nullable().default(''),
  badge_free_text: z.string().nullable().optional().default(''),
  selected_no_affiliated_party: z.boolean().default(false).optional(),
  referrer_id: z.number().optional().nullable(),
  enable_referrer_id: z.boolean().default(false).optional(),
  family_frame_name: z.string().nullable().optional().default(''),
  protocol_leader_circles: z.array(ProtocolCircleDetailsSchema).default([]),
  user_poster_photos: z.array(UserPosterPhotoSchema).default([]),
});

export type LayoutCreationInfoResponse = z.infer<typeof LayoutCreationInfoResponseSchema>;
