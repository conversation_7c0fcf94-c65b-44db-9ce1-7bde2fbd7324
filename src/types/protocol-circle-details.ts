import { z } from 'zod';
import { PhotoSchema } from '@/types/photo';
import { HeaderPhotoType } from '@/enums/header-photo-type';
import { nanoid } from 'nanoid';

/**
 * Please attach `nano_id` with method `attachNanoIdToProtocolCircle` if you want to use it.
 * */
export const ProtocolCircleDetailsSchema = z.object({
  nano_id: z.string().optional().nullable(),
  priority: z.number().optional().nullable(),
  name: z.string(),
  name_en: z.string().default(''),
  short_name: z.string().optional().nullable(),
  sub_text: z.string().nullable().optional().default(''),
  id: z.number().nullable().optional(),
  type: z.nativeEnum(HeaderPhotoType).optional().default(HeaderPhotoType.header1),
  photo_file: z.instanceof(File).nullable().optional(),
  poster_photos: z.array(PhotoSchema).default([]),
});

export type ProtocolCircleDetails = z.infer<typeof ProtocolCircleDetailsSchema>;

/**
 * This Method Attach a NanoId to the Protocol Circle
 * Use Case: We have feature to select different protocol image for the same circle. For that we need
 * Unique Identifier for adding in List and Sorting (Dragging).
 * So, we are attaching a NanoId to the Protocol Circle.
 * */
export const attachNanoIdToProtocolCircle = (
  circle: ProtocolCircleDetails
): ProtocolCircleDetails => {
  return {
    ...circle,
    nano_id: nanoid(),
  };
};
