import { z } from 'zod';

export enum BadgeBanner {
  gold = 'GOLD',
  silver = 'SILVER',
  white = 'WHITE',
  none = 'NONE',
}

export enum BadgeRing {
  goldRing = 'GOLD_RING',
  silverRing = 'SILVER_RING',
  noRing = 'NO_RING',
}

export const BadgeSchema = z.object({
  id: z.number(),
  active: z.boolean(),
  badgeIconUrl: z.string().nullable().optional(),
  badgeRole: z.string().default(''),
  badgeBanner: z.nativeEnum(BadgeBanner).default(BadgeBanner.none),
  badgeRing: z.nativeEnum(BadgeRing).default(BadgeRing.noRing),
  description: z.string(),
});

export type Badge = z.infer<typeof BadgeSchema>;
