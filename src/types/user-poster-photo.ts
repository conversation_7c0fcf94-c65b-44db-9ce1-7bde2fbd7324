import { z } from 'zod';
import { PhotoSchema } from '@/types/photo';

export enum UserPosterPhotoType {
  posterPhotoWithoutBackgroundOriginal = 'poster_photo_without_background_original',
  heroFramePhotoOriginal = 'hero_frame_photo_original',
  familyFramePhotoOriginal = 'family_frame_photo_original',
}

export const UserPosterPhotoSchema = z.object({
  type: z.nativeEnum(UserPosterPhotoType),
  photo: PhotoSchema.nullable().optional(),
  photo_file: z.instanceof(File).nullable().optional(),
});

export type UserPosterPhoto = z.infer<typeof UserPosterPhotoSchema>;
