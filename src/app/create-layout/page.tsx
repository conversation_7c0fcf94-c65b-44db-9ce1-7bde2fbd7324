import CreateLayoutUi from '@/components/create-layout/create-layout-ui';
import { LayoutCreationInfoResponse } from '@/types/layout-creation-info-response';
import createLayoutService from '@/services/create-layout-service';
import { ApiException } from '@/exceptions/api-exception';
import { headers } from 'next/headers';
import { notFound, redirect } from 'next/navigation';
import { Box, Typography } from '@mui/material';
import Grid from '@mui/material/Grid2';
import { environmentConfig } from '@/config/environment';

type Props = {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

async function fetchLayoutInfoData(
  user_id: string,
  cookieHeader: string
): Promise<LayoutCreationInfoResponse> {
  console.debug('Fetching for User Id: ', user_id);
  try {
    return await createLayoutService.fetchLayoutCreationInfo({
      user_id,
      headers: {
        Cookie: cookieHeader,
      },
    });
  } catch (error) {
    console.error('Error fetching layout creation info:', error);
    const errorMessage = ApiException.getErrorMessage(
      error,
      'An unexpected error occurred while fetching layout information.'
    );
    throw new Error(errorMessage);
  }
}

async function CreateLayoutPage(props: Props) {
  const searchParams = await props.searchParams;
  const user_id = (searchParams?.user_id as string) ?? '';
  if (!user_id) {
    return notFound();
  }

  const headersList = await headers();

  const redirectToAdminDashboard = () => {
    const host = headersList.get('host');
    const protocol =
      process.env.NODE_ENV === 'production'
        ? 'https'
        : headersList.get('x-forwarded-proto') || 'https';
    console.debug('host, protocol: ', host, protocol);
    // Construct fallback URL with query params (if referer is null it will help)
    const currentUrl = `${protocol}://${host}/create-layout?user_id=${user_id}`;
    const encodedRedirectUrl = encodeURIComponent(currentUrl);

    // Get redirect URL based on environment config
    console.debug('Redirect URL: ', encodedRedirectUrl);
    redirect(`${environmentConfig.adminUrl}/login?redirect_url=${encodedRedirectUrl}`);
  };

  const cookieHeader = headersList.get('cookie');

  console.debug('Cookie Header: ', cookieHeader);

  if (!cookieHeader) {
    redirectToAdminDashboard();
    return;
  }

  let containsValidAdminSession: boolean;

  try {
    await createLayoutService.validateAdminSession(cookieHeader);
    containsValidAdminSession = true;
  } catch {
    containsValidAdminSession = false;
  }

  if (!containsValidAdminSession) {
    redirectToAdminDashboard();
    return;
  }

  try {
    const response = await fetchLayoutInfoData(user_id, cookieHeader);
    return <CreateLayoutUi response={response} />;
  } catch (error: unknown) {
    console.error('Error fetching layout creation info:', error);
    const errorMessage = ApiException.getErrorMessage(
      error,
      'An unexpected error occurred while loading the page.'
    );

    return (
      <Grid
        size={12}
        container
        direction="column"
        justifyContent="center"
        alignItems="center"
        spacing={4}
        sx={{ height: '100vh' }}
      >
        <Typography
          variant="h5"
          fontWeight="bold"
          align="center"
          textAlign="center"
          color="error"
        >
          The following error occurred while loading the page:
        </Typography>
        <Box
          sx={{
            padding: '1rem',
            boxShadow: '0px 0px 16px red',
            backgroundColor: '#f8d7da',
          }}
        >
          <Typography
            variant="h5"
            fontWeight="bold"
            align="center"
            textAlign="center"
            color="error"
          >
            {errorMessage}
          </Typography>
        </Box>
        <Typography
          textAlign="center"
          fontSize={16}
          color="#666"
        >
          You can close this tab now
        </Typography>
      </Grid>
    );
  }
}

export default CreateLayoutPage;
