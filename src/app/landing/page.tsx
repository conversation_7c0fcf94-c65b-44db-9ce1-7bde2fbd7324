'use client';

import { Container, Typography } from '@mui/material';
import Image from 'next/image';
import Grid from '@mui/material/Grid2';
import LandingPageActions from '@/components/landing/landing-page-actions';

function LandingPage() {
  return (
    <Container
      disableGutters
      maxWidth={false}
      sx={{
        position: 'relative',
        height: '100vh',
        width: '100vw',
      }}
    >
      <Image
        src="/assets/landing/jathara_wallpaper.jpg"
        alt="Jathara Image"
        fill
        style={{ objectFit: 'cover' }}
      />
      <Grid
        container
        direction="column"
        alignItems="center"
        spacing={4}
        sx={{
          position: 'absolute',
          bottom: '20%',
          left: '0',
          zIndex: 1,
          width: '100%',
        }}
      >
        <Grid>
          <Typography
            fontSize={48}
            fontWeight="bold"
            textAlign="center"
            sx={{ color: '#fff', mb: 4 }}
          >
            Welcome to Jathara!
          </Typography>
        </Grid>
        <Grid
          size={12}
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <LandingPageActions />
        </Grid>
      </Grid>
    </Container>
  );
}

export default LandingPage;
