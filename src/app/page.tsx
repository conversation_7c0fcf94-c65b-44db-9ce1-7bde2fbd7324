import LandingPage from '@/app/landing/page';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: '<PERSON><PERSON><PERSON>',
  description: 'Internal Tool for Praja',
  keywords:
    'jathara,praja internal tool, praja, praja app, praja buzz, local trends, political trends, politician circles',
  authors: {
    name: 'Circleapp Online Services Pvt. Ltd.',
    url: 'https://github.com/praja',
  },
  openGraph: {
    siteName: 'Jathara',
    url: 'https://jathara.thecircleapp.in',
    title: 'J<PERSON><PERSON>',
    description: 'Internal Tool for Praja',
    type: 'website',
    images: [
      {
        url: 'https://jathara.thecircleapp.in/hero-bg-3.jpg',
        secureUrl: 'https://jathara.thecircleapp.in/hero-bg-3.jpg',
        type: 'image/jpeg',
        alt: 'Indians parading with India flag',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@PrajaBuzz',
  },
  icons: {
    icon: [
      { rel: 'icon', url: '/favicon.ico', type: 'image/x-icon' },
      { rel: 'icon', url: '/favicon-16x16.png', type: 'image/png', sizes: '16x16' },
      { rel: 'icon', url: '/favicon-32x32.png', type: 'image/png', sizes: '32x32' },
    ],
    apple: {
      rel: 'apple-touch-icon.png',
      url: '/apple-touch-icon.png',
      type: 'image/png',
      sizes: '180x180',
    },
  },
};

export default function Home() {
  return <LandingPage />;
}
