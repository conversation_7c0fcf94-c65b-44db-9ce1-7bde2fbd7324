/**
 * Environment configuration for the application
 * This file provides environment-specific configuration values based on the current URL
 */

export type EnvironmentType = 'development' | 'preprod' | 'production';

export interface EnvironmentConfig {
  apiBaseUrl: string;
  appUrl: string;
  adminUrl: string;
}

// 1. Central URL map
const URLS: Record<EnvironmentType, EnvironmentConfig> = {
  development: {
    apiBaseUrl: 'http://localhost:3000',
    appUrl: 'http://localhost:3001',
    adminUrl: 'http://localhost:3000/admin',
  },
  preprod: {
    apiBaseUrl: 'https://preprod-api.thecircleapp.in',
    appUrl: 'https://preprod-jathara.thecircleapp.in',
    adminUrl: 'https://preprod-api.thecircleapp.in/admin',
  },
  production: {
    apiBaseUrl: 'https://api.thecircleapp.in',
    appUrl: 'https://jathara.thecircleapp.in',
    adminUrl: 'https://www.thecircleapp.in/admin',
  },
};

export function getCurrentEnvironment(): EnvironmentType {
  // allow explicit override (e.g., CI or build‐time)
  if (process.env.NEXT_PUBLIC_DEPLOYMENT) {
    return process.env.NEXT_PUBLIC_DEPLOYMENT as EnvironmentType;
  }
  // server
  return process.env.NODE_ENV === 'development' ? 'development' : 'production';
}

export function getConfigForEnvironment(env: EnvironmentType): EnvironmentConfig {
  console.debug('environment', env);
  return URLS[env];
}

// Export a singleton instance for easy client-side access
export const environmentConfig = getConfigForEnvironment(getCurrentEnvironment());
