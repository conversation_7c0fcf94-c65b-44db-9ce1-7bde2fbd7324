import { UserDetails } from '@/types/user-details';
import { Photo } from '@/types/photo';
import { CircleDetails, CircleDetailsSchema } from '@/types/circle-details';
import { Badge, BadgeBanner, BadgeRing } from '@/types/badge';
import { UserPosterPhoto, UserPosterPhotoType } from '@/types/user-poster-photo';
import { ProtocolCircleDetails } from '@/types/protocol-circle-details';
import { LayoutCreationInfoResponse } from '@/types/layout-creation-info-response';
import { HeaderPhotoType } from '@/enums/header-photo-type';

// Mock UserDetails
function getUserDetailsMock(): UserDetails {
  return {
    id: 1550298,
    name: 'Pawan Kalyan',
    phone: 1234567890,
    photo: getPhotoMock(),
    village: getVillageCircleDetailsMock(),
    mandal: getMandalCircleDetailsMock(),
    district: getDistrictCircleDetailsMock(),
    state: getStateCircleDetailsMock(),
    badge: getBadgeMock(),
    affiliated_party: getLeaderCircleDetailsMock(),
    profession: 'Actor',
    dob: '1995-01-01',
  };
}

// Mock CircleDetails
function getVillageCircleDetailsMock(): CircleDetails {
  return CircleDetailsSchema.parse({
    id: 31,
    name: 'Ullagallu',
    name_en: 'Ullagallu',
    short_name: 'Ullagallu',
  });
}

function getMandalCircleDetailsMock(): CircleDetails {
  return CircleDetailsSchema.parse({
    id: 32,
    name: 'Mundlamuru',
    name_en: 'Mundlamuru',
    short_name: 'Mundlamuru',
  });
}

function getDistrictCircleDetailsMock(): CircleDetails {
  return CircleDetailsSchema.parse({
    id: 33,
    name: 'Prakasam',
    name_en: 'Prakasam',
    short_name: 'Prakasam',
  });
}

function getStateCircleDetailsMock(): CircleDetails {
  return CircleDetailsSchema.parse({
    id: 34,
    name: 'Andhra Pradesh',
    name_en: 'Andhra Pradesh',
    short_name: 'AP',
  });
}

function getLeaderCircleDetailsMock(): CircleDetails {
  return CircleDetailsSchema.parse({
    id: 31402,
    name: 'Janasena Party',
    name_en: 'Janasena Party',
    short_name: 'JSP',
  });
}

// Mock Photo
export function getPhotoMock(): Photo {
  return {
    id: 1,
    url: 'https://az-cdn.thecircleapp.in/production/admin-media/40/ee560652-d168-4198-9906-d3b3991de417.jpg',
    placeholderUrl:
      'https://az-cdn.thecircleapp.in/production/admin-media/40/ee560652-d168-4198-9906-d3b3991de417.jpg',
    selected: true,
  };
}

export function getPhotoMock2(): Photo {
  return {
    id: 2,
    url: 'https://az-cdn.thecircleapp.in/production/photos/41/209c140a-b614-4604-aa09-36632d48c56d.png',
    placeholderUrl:
      'https://az-cdn.thecircleapp.in/production/photos/41/209c140a-b614-4604-aa09-36632d48c56d.png',
    selected: false,
  };
}

// Mock Badge
function getBadgeMock(): Badge {
  return {
    id: 1,
    active: true,
    badgeIconUrl:
      'https://az-cdn.thecircleapp.in/production/admin-media/40/ee560652-d168-4198-9906-d3b3991de417.jpg',
    badgeRole: 'Top Fan JSP',
    badgeBanner: BadgeBanner.gold,
    badgeRing: BadgeRing.goldRing,
    description: 'Top Fan JSP',
  };
}

// Mock ProtocolCircleDetails
function getProtocolCircleDetailsMock(): ProtocolCircleDetails {
  return {
    priority: 1,
    name: 'Pawan Kalyan Konidela',
    name_en: 'Pawan Kalyan Konidela',
    sub_text: 'JSP Party Leader',
    id: 9999,
    type: HeaderPhotoType.header1,
    photo_file: null,
    poster_photos: [getPhotoMock(), getPhotoMock2()],
  };
}

function getProtocolCircleDetailsMock2(): ProtocolCircleDetails {
  return {
    priority: 2,
    name: 'Nara Chandrababu Naidu',
    name_en: 'Nara Chandrababu Naidu',
    sub_text: 'TDP, CM of AP',
    type: HeaderPhotoType.header2,
    photo_file: null,
    poster_photos: [getPhotoMock(), getPhotoMock2()],
  };
}
// Mock UserPosterPhoto
function getUserPosterPhotoMock(): UserPosterPhoto {
  return {
    type: UserPosterPhotoType.posterPhotoWithoutBackgroundOriginal,
    photo: getPhotoMock(),
    photo_file: null,
  };
}

// Mock LayoutCreationInfoResponse
function getLayoutCreationInfoResponseMock(): LayoutCreationInfoResponse {
  return {
    user: getUserDetailsMock(),
    remarks: 'Test remarks',
    badge_free_text: 'Exclusive Member',
    selected_no_affiliated_party: false,
    referrer_id: 123456,
    enable_referrer_id: false,
    family_frame_name: 'Family Frame Name',
    protocol_leader_circles: [getProtocolCircleDetailsMock(), getProtocolCircleDetailsMock2()],
    user_poster_photos: [getUserPosterPhotoMock()],
  };
}

export { getUserDetailsMock, getLayoutCreationInfoResponseMock };
