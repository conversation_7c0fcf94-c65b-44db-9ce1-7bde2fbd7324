import { useCallback, useRef, useEffect } from 'react';

const useBlobUrlManager = () => {
  const trackedBlobUrls = useRef<Set<string>>(new Set());
  const fileToUrlMap = useRef<Map<File, string>>(new Map());

  const createTrackedBlobUrl = useCallback((file: File): string => {
    const existingUrl = fileToUrlMap.current.get(file);
    if (existingUrl && trackedBlobUrls.current.has(existingUrl)) {
      return existingUrl;
    }
    const url = URL.createObjectURL(file);
    trackedBlobUrls.current.add(url);
    fileToUrlMap.current.set(file, url);
    return url;
  }, []);

  const revokeBlobUrl = useCallback((url: string) => {
    if (trackedBlobUrls.current.has(url)) {
      URL.revokeObjectURL(url);
      trackedBlobUrls.current.delete(url);
      for (const [file, fileUrl] of fileToUrlMap.current.entries()) {
        if (fileUrl === url) {
          fileToUrlMap.current.delete(file);
          break;
        }
      }
    }
  }, []);

  const revokeBlobUrlForFile = useCallback((file: File) => {
    const url = fileToUrlMap.current.get(file);
    if (url) {
      revokeBlobUrl(url);
    }
  }, [revokeBlobUrl]);

  const revokeAllBlobUrls = useCallback(() => {
    trackedBlobUrls.current.forEach((url) => {
      URL.revokeObjectURL(url);
    });
    trackedBlobUrls.current.clear();
    fileToUrlMap.current.clear();
  }, []);

  const getBlobUrlForPhotoFile = useCallback((file: File): string => {
    return createTrackedBlobUrl(file);
  }, [createTrackedBlobUrl]);

  useEffect(() => {
    return () => {
      revokeAllBlobUrls();
    };
  }, [revokeAllBlobUrls]);

  return {
    createTrackedBlobUrl,
    revokeBlobUrl,
    revokeBlobUrlForFile,
    revokeAllBlobUrls,
    getBlobUrlForPhotoFile,
  };
};

export default useBlobUrlManager;
