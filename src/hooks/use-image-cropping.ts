import { useState, useCallback } from 'react';

export interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

const useImageCropping = () => {
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<CropArea | null>(null);

  const handleCropComplete = useCallback((_: CropArea, croppedAreaPixels: CropArea) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const getCroppedImg = useCallback(async (
    imageSrc: string,
    cropPixels: CropArea,
    fileType: string,
    outputDimensions = { width: 250, height: 280 }
  ) => {
    const createImage = (url: string): Promise<HTMLImageElement> => {
      return new Promise((resolve, reject) => {
        const image = new window.Image();
        image.addEventListener('load', () => resolve(image));
        image.addEventListener('error', (error) => reject(error));
        image.setAttribute('crossOrigin', 'anonymous');
        image.src = url;
      });
    };

    try {
      const image = await createImage(imageSrc);
      const canvas = document.createElement('canvas');
      canvas.width = outputDimensions.width;
      canvas.height = outputDimensions.height;
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Failed to get 2D context from canvas');

      // Validate crop dimensions
      if (cropPixels.width <= 0 || cropPixels.height <= 0) {
        throw new Error('Invalid crop dimensions');
      }

      ctx.drawImage(
        image,
        cropPixels.x,
        cropPixels.y,
        cropPixels.width,
        cropPixels.height,
        0,
        0,
        outputDimensions.width,
        outputDimensions.height
      );

      return new Promise<Blob>((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to create blob from canvas'));
          }
        }, fileType);
      });
    } catch (error) {
      throw new Error(`Image cropping failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, []);

  const resetCropping = useCallback(() => {
    setCrop({ x: 0, y: 0 });
    setZoom(1);
    setCroppedAreaPixels(null);
  }, []);

  return {
    crop,
    setCrop,
    zoom,
    setZoom,
    croppedAreaPixels,
    handleCropComplete,
    getCroppedImg,
    resetCropping,
  };
};

export default useImageCropping;
