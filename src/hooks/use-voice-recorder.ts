'use client';

import { useState, useEffect } from 'react';
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';

export function useVoiceRecorder(language: string = 'te-IN') {
  const { transcript, listening, resetTranscript, browserSupportsSpeechRecognition } =
    useSpeechRecognition({
      transcribing: true,
      clearTranscriptOnListen: true,
    });

  const [errorMsg, setErrorMsg] = useState('');

  const startRecording = async () => {
    setErrorMsg('');
    if (!browserSupportsSpeechRecognition) {
      setErrorMsg("Your browser doesn't support speech recognition.");
      return;
    }

    try {
      resetTranscript();
      await SpeechRecognition.startListening({
        language,
        continuous: false,
        interimResults: false,
      });
    } catch (error: unknown) {
      if (error instanceof Error) {
        setErrorMsg('Error starting speech recognition: ' + error.message);
      } else {
        setErrorMsg('Error starting speech recognition.');
      }
      resetTranscript();
    }
  };

  const stopRecording = async () => {
    await SpeechRecognition.stopListening();
  };

  // Cleanup on unmount to ensure speech recognition is stopped
  useEffect(() => {
    return () => {
      void stopRecording();
    };
  }, []);

  return { transcript, listening, errorMsg, startRecording, stopRecording, resetTranscript };
}
