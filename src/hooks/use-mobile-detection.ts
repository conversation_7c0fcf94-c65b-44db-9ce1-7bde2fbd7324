import { useEffect, useState } from 'react';

const useMobileDetection = (): boolean => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    let mounted = true;
    const detectMobile = async () => {
      const deviceDetect = await import('react-device-detect');
      if (mounted) setIsMobile(deviceDetect.isMobile);
    };
    detectMobile();
    return () => {
      mounted = false;
    };
  }, []);

  return isMobile;
};

export default useMobileDetection;
