/** Do not edit this file unless if you want to add new HTTP Methods */

import AppClient from '@/services/app-client';
import { AxiosResponse } from 'axios';

class ApiService {
  public async get<T>(
    url: string,
    params?: Record<string, string | number | boolean>,
    { headers }: { headers: Record<string, string> } = { headers: {} }
  ): Promise<T> {
    const response: AxiosResponse<T> = await AppClient.get<T>(url, {
      params: params,
      headers: headers,
    });
    return response.data;
  }

  public async post<T>(
    url: string,
    data: object,
    { headers }: { headers: Record<string, string> } = { headers: {} }
  ): Promise<T> {
    const response: AxiosResponse<T> = await AppClient.post<T>(url, data, {
      headers: headers,
    });
    return response.data;
  }

  public async put<T>(url: string, data: object): Promise<T> {
    const response: AxiosResponse<T> = await AppClient.put<T>(url, data);
    return response.data;
  }

  public async delete<T>(url: string): Promise<T> {
    const response: AxiosResponse<T> = await AppClient.delete<T>(url);
    return response.data;
  }
}

export default ApiService;
