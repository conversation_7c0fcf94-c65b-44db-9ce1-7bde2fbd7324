import ApiService from '@/services/api-service';
import { CircleDetails, CircleDetailsSchema } from '@/types/circle-details';
import { z } from 'zod';
import {
  LayoutCreationInfoResponse,
  LayoutCreationInfoResponseSchema,
} from '@/types/layout-creation-info-response';
import {
  ProtocolCircleDetails,
  ProtocolCircleDetailsSchema,
} from '@/types/protocol-circle-details';
import { ProtocolPhotoDetails, ProtocolPhotoDetailsSchema } from '@/types/protocol-photo-details';
import { DashboardLinkResponse } from '@/types/dashboard-link-response';

class CreateLayoutService extends ApiService {
  public async validateAdminSession(cookieHeader: string): Promise<void> {
    try {
      await this.get(
        '/admin/validate-admin-session',
        {},
        {
          headers: {
            Cookie: cookieHeader,
          },
        }
      );
    } catch (error) {
      throw error;
    }
  }

  public async fetchLayoutCreationInfo({
    user_id,
    headers,
  }: {
    user_id: string;
    headers: Record<string, string>;
  }): Promise<LayoutCreationInfoResponse> {
    try {
      const response = await this.get(
        '/admin/layout-creation-info',
        {
          user_id,
        },
        {
          headers: {
            ...headers,
          },
        }
      );
      return LayoutCreationInfoResponseSchema.parse(response);
    } catch (error) {
      throw error;
    }
  }

  /**
   Temporary function to fetch the dashboard link for RM, BOE, and OE.
   */
  public async getDashboardLink({
    user_id,
    type,
  }: {
    user_id: string;
    type: 'rm' | 'boe' | 'oe';
  }): Promise<DashboardLinkResponse> {
    try {
      const response = await this.get('/admin/dashboard-link', {
        user_id,
        type,
      });
      return DashboardLinkResponse.parse(response);
    } catch (error) {
      console.error('Error fetching dashboard link:', error);
      throw new Error('Failed to fetch dashboard link');
    }
  }

  public async fetchPartyCircles({ searchTerm }: { searchTerm: string }): Promise<CircleDetails[]> {
    try {
      const response = await this.get('/admin/party-circles', {
        term: searchTerm,
      });
      return z.array(CircleDetailsSchema).parse(response);
    } catch (error) {
      console.error('Error fetching affiliated party circles:', error);
      throw new Error('Failed to fetch affiliated party circles');
    }
  }

  public async fetchCirclesForProtocol({
    searchTerm,
    userId,
  }: {
    searchTerm: string;
    userId: number;
  }): Promise<ProtocolCircleDetails[]> {
    try {
      const response = await this.get('/admin/search-circles-for-protocol', {
        term: searchTerm,
        user_id: userId,
      });
      return z.array(ProtocolCircleDetailsSchema).parse(response);
    } catch (error) {
      console.error('Error fetching circles for protocol:', error);
      throw new Error('Failed to fetch circles for protocol');
    }
  }

  public async fetchDefaultLeaderCirclesOfParty({
    posterAffiliatedPartyId,
  }: {
    posterAffiliatedPartyId: number;
  }): Promise<ProtocolCircleDetails[]> {
    try {
      const response = await this.get('/admin/default-leader-circles-of-a-party', {
        poster_affiliated_party_id: posterAffiliatedPartyId,
      });
      return z.array(ProtocolCircleDetailsSchema).parse(response);
    } catch (error) {
      console.error('Error fetching default leader circles of party:', error);
      throw new Error('Failed to fetch default leader circles of party');
    }
  }

  public async fetchProtocolXYPositions({
    h1Count,
    h2Count,
  }: {
    h1Count: number;
    h2Count: number;
  }): Promise<ProtocolPhotoDetails[]> {
    try {
      const response = await this.get('/admin/protocol-xy-positions', {
        h1_count: h1Count,
        h2_count: h2Count,
      });
      return z.array(ProtocolPhotoDetailsSchema).parse(response);
    } catch (error) {
      console.error('Error fetching protocol xy positions:', error);
      throw new Error('Failed to fetch protocol xy positions');
    }
  }

  public async saveLayoutAsDraft({ data }: { data: FormData }) {
    try {
      return await this.post('/admin/save-layout-as-draft', data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    } catch (error) {
      throw error;
    }
  }
}

const createLayoutService = new CreateLayoutService();
export default createLayoutService;
