# Base stage: use Node 23 (Alpine)
FROM node:23-alpine AS base
RUN apk add --no-cache g++ make py3-pip libc6-compat
WORKDIR /app
COPY package*.json ./
EXPOSE 3000

# Builder stage: install dependencies and build the app
FROM base AS builder
WORKDIR /app

# Define Build Arguments
ARG NEXT_PUBLIC_DEPLOYMENT
ENV NEXT_PUBLIC_DEPLOYMENT=${NEXT_PUBLIC_DEPLOYMENT}

RUN npm install
COPY . .
# This command should trigger a standalone build (make sure your next.config.js is set with output: "standalone")
RUN npm run build

# Development stage: for local development
FROM base AS dev
ENV NODE_ENV=development
RUN npm install
COPY . .
CMD npm run dev

# Production stage: run the standalone bundle
FROM node:23-alpine AS production
WORKDIR /app
ENV NODE_ENV=production

# Create a non-root user (optional, for security)
RUN addgroup -g 1001 -S nodejs && adduser -S nextjs -u 1001
USER nextjs

# Copy only the necessary files from the builder stage.
# When using output: "standalone", Next.js puts the production bundle in .next/standalone.
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
# Also copy any required assets
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/public ./public
COPY --from=builder /app/next.config.ts ./next.config.ts
# Copy static assets to ensure Next.js can serve them.
COPY --from=builder /app/.next/static ./.next/static

CMD npm start
