# Jathara Project Guide

## Commands
- `npm run dev`: Run development server with Turbopack
- `npm run build`: Build the project
- `npm run start`: Start the production server
- `npm run lint`: Run ESLint
- `npm run format`: Run Prettier (add this script to package.json: `"format": "prettier --write ."`)

## Code Style Guidelines
- **Imports**: Use absolute imports from `@/*` for project files 
- **Formatting**: ESLint with Next.js config + Prettier for consistent code style
- **Types**: TypeScript with strict mode; explicit types for functions, props, and state
- **Naming**: PascalCase for components/interfaces, camelCase for variables/functions
- **Components**: Functional components with TypeScript interfaces for props
- **Error Handling**: Use try/catch blocks with appropriate error logging
- **File Structure**: Follow Next.js App Router conventions
- **State Management**: Use React hooks for component state

## Project Architecture
- Next.js 15.2.1 with App Router
- React 19.0.0
- TypeScript 5.0+
- TailwindCSS 4.0+
- Material UI v6
- DND Kit for drag-and-drop functionality