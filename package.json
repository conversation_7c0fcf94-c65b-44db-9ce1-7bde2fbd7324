{"name": "jaa<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.7", "@mui/material": "^6.4.7", "@mui/material-nextjs": "^6.4.3", "@mui/x-date-pickers": "^7.28.0", "axios": "^1.8.2", "dayjs": "^1.11.13", "nanoid": "^5.1.4", "next": "15.2.1", "react": "^19.0.0", "react-device-detect": "^2.2.3", "react-dom": "^19.0.0", "react-easy-crop": "^5.4.2", "react-speech-recognition": "^4.0.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-speech-recognition": "^3.9.6", "@typescript-eslint/eslint-plugin": "^8.26.0", "@typescript-eslint/parser": "^8.26.0", "eslint": "^9", "eslint-config-next": "15.2.1", "eslint-config-prettier": "^10.0.2", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5"}}