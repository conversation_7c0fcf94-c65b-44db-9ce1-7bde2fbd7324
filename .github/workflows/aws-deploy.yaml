name: <PERSON><PERSON> Deploy
on:
  push:
    branches: [ "master" ]
concurrency:
  cancel-in-progress: true
  group: jathara-aws-deploy
env:
  ENVIRONMENT: production
  ENVIRONMENT_NAME: Production
  ECR_REPOSITORY: jathara
jobs:
  build:
    runs-on: aws-arc-runners
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_ACCESS_TOKEN }}

      - uses: benjlevesque/short-sha@v2.2
        id: short-sha

      - run: echo $SHA
        env:
          SHA: ${{ steps.short-sha.outputs.sha }}

      - run: echo $SHA
        env:
          SHA: ${{ env.SHA }}

      - name: Slack Notification
        uses: slackapi/slack-github-action@v1.24.0
        with:
          payload: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "text": "<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|*[${{ env.ENVIRONMENT_NAME }}] Deployment started for repository jathara*>",
                    "type": "mrkdwn"
                  },
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Branch*"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Commit Id*"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${{ github.server_url }}/${{ github.repository }}/tree/${{ github.ref_name }}|*${{ github.ref_name }}*>"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|*${{ env.SHA }}*>"
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push image to Amazon ECR
        id: build-image-prod
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build . --file Dockerfile --target=production -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT
  deploy:
    runs-on: ubuntu-latest
    needs:
      - build
    steps:
      - name: Update tag
        run: 'curl -L -X POST -H "Accept:application/vnd.github+json" -H "Authorization:Bearer ${{ secrets.GH_TOKEN }}" -H "X-GitHub-Api-Version:2022-11-28" https://api.github.com/repos/praja/fleet-infra/actions/workflows/71647658/dispatches -d ''{"ref" : "master","inputs": { "imageTag": "${{github.sha}}","cluster": "aws","chart": "jathara" }}'''
