name: Verify Build

on:
  pull_request:
    branches:
      - master

jobs:
  build:
    runs-on: aws-arc-runners
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Node.js 23.xx
        uses: actions/setup-node@v4
        with:
          node-version: '23.x'

      - name: Install dependencies
        run: npm install

      - name: Build Project
        run: npm run build
